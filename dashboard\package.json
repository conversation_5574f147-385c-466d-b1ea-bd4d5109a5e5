{"name": "fraud-detection-dashboard", "version": "2.0.0", "private": true, "dependencies": {"@types/jest": "^27.5.2", "@types/node": "^16.11.47", "@types/react": "^18.0.15", "@types/react-dom": "^18.0.6", "axios": "^1.4.0", "chart.js": "^4.3.0", "react": "^18.2.0", "react-chartjs-2": "^5.2.0", "react-dom": "^18.2.0", "react-router-dom": "^6.11.2", "react-scripts": "^5.0.1", "tailwindcss": "^3.3.0", "typescript": "^4.7.4", "web-vitals": "^2.1.4"}, "devDependencies": {"@craco/craco": "^7.1.0", "@tailwindcss/forms": "^0.5.10", "autoprefixer": "^10.4.14", "postcss": "^8.4.24"}, "scripts": {"start": "craco start", "build": "craco build", "test": "craco test", "eject": "react-scripts eject"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "proxy": "http://localhost:8000"}