import React, { createContext, useContext, useState, useEffect, ReactNode, useRef } from 'react';
import { WebSocketMessage } from '../types';

interface WebSocketContextType {
  connected: boolean;
  lastMessage: string | null;
  error: string | null;
  sendMessage: (message: string) => void;
  reconnect: () => void;
}

// Create WebSocket context
const WebSocketContext = createContext<WebSocketContextType | null>(null);

interface WebSocketProviderProps {
  children: ReactNode;
}

// WebSocket provider component
export const WebSocketProvider: React.FC<WebSocketProviderProps> = ({ children }) => {
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [connected, setConnected] = useState<boolean>(false);
  const [lastMessage, setLastMessage] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef<number>(0);
  const maxReconnectAttempts = 5;

  // Function to connect to WebSocket
  const connectWebSocket = (): void => {
    const wsUrl = process.env.REACT_APP_WS_URL || 'ws://localhost:8000/ws';

    try {
      const ws = new WebSocket(wsUrl);

      ws.onopen = () => {
        console.log('WebSocket connected');
        setConnected(true);
        setError(null);
        reconnectAttemptsRef.current = 0;
      };

      ws.onmessage = (event: MessageEvent) => {
        try {
          // Validate message format
          const data = JSON.parse(event.data) as WebSocketMessage;
          setLastMessage(event.data);
        } catch (parseError) {
          console.error('Error parsing WebSocket message:', parseError);
          setLastMessage(event.data); // Store raw data if parsing fails
        }
      };

      ws.onerror = (event: Event) => {
        console.error('WebSocket error:', event);
        setError('WebSocket connection error');
      };

      ws.onclose = (event: CloseEvent) => {
        console.log('WebSocket disconnected:', event.code, event.reason);
        setConnected(false);

        // Attempt to reconnect with exponential backoff
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          const delay = Math.pow(2, reconnectAttemptsRef.current) * 1000; // Exponential backoff
          reconnectAttemptsRef.current += 1;

          reconnectTimeoutRef.current = setTimeout(() => {
            console.log(`Attempting to reconnect (${reconnectAttemptsRef.current}/${maxReconnectAttempts})...`);
            connectWebSocket();
          }, delay);
        } else {
          setError('Failed to reconnect after maximum attempts');
        }
      };

      setSocket(ws);
    } catch (connectionError) {
      console.error('Failed to create WebSocket connection:', connectionError);
      setError('Failed to create WebSocket connection');
    }
  };

  // Send message function
  const sendMessage = (message: string): void => {
    if (socket && socket.readyState === WebSocket.OPEN) {
      socket.send(message);
    } else {
      console.warn('WebSocket is not connected');
    }
  };

  // Manual reconnect function
  const reconnect = (): void => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
    }
    reconnectAttemptsRef.current = 0;
    setError(null);
    connectWebSocket();
  };

  // Connect to WebSocket on mount
  useEffect(() => {
    connectWebSocket();

    // Clean up on unmount
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
      if (socket) {
        socket.close();
      }
    };
  }, []); // eslint-disable-line react-hooks/exhaustive-deps

  // Context value
  const value: WebSocketContextType = {
    connected,
    lastMessage,
    error,
    sendMessage,
    reconnect
  };

  return (
    <WebSocketContext.Provider value={value}>
      {children}
    </WebSocketContext.Provider>
  );
};

// Custom hook for using WebSocket context
export const useWebSocket = (): WebSocketContextType => {
  const context = useContext(WebSocketContext);
  if (!context) {
    throw new Error('useWebSocket must be used within a WebSocketProvider');
  }
  return context;
};
