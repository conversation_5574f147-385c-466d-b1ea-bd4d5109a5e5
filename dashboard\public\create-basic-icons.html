<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Fraud Detection Platform - Icon Generator</title>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
        }
        h1 {
            color: #3b82f6;
            text-align: center;
            margin-bottom: 30px;
        }
        .icon-preview {
            display: flex;
            gap: 20px;
            justify-content: center;
            margin: 30px 0;
            flex-wrap: wrap;
        }
        .icon-item {
            text-align: center;
            padding: 15px;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            background: #f8fafc;
        }
        .icon-item canvas {
            display: block;
            margin: 0 auto 10px;
            border: 1px solid #d1d5db;
        }
        .generate-btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            display: block;
            margin: 20px auto;
        }
        .generate-btn:hover {
            background: #2563eb;
        }
        .instructions {
            background: #f0f9ff;
            border: 1px solid #bae6fd;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        .success {
            background: #f0fdf4;
            border: 1px solid #bbf7d0;
            color: #166534;
            padding: 15px;
            border-radius: 6px;
            margin: 20px 0;
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Fraud Detection Platform - Icon Generator</h1>
        
        <div class="instructions">
            <h3>📋 Instructions:</h3>
            <ol>
                <li>Click "Generate Icons" to create the PNG files</li>
                <li>Right-click each icon and "Save image as..."</li>
                <li>Save them in the <code>dashboard/public/</code> directory with the exact names shown</li>
                <li>Refresh your application to see the new icons</li>
            </ol>
        </div>

        <button class="generate-btn" onclick="generateIcons()">🎨 Generate Icons</button>

        <div class="icon-preview" id="iconPreview"></div>

        <div class="success" id="successMessage">
            ✅ Icons generated successfully! Right-click each icon above and "Save image as..." to download them.
        </div>
    </div>

    <script>
        function drawFraudIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            canvas.width = size;
            canvas.height = size;
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle
            ctx.fillStyle = '#3b82f6';
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
            ctx.fill();
            
            // Shield shape
            ctx.fillStyle = 'white';
            ctx.globalAlpha = 0.9;
            ctx.beginPath();
            const centerX = size/2;
            const centerY = size/2;
            const shieldWidth = size * 0.6;
            const shieldHeight = size * 0.7;
            
            ctx.moveTo(centerX, centerY - shieldHeight/2);
            ctx.lineTo(centerX + shieldWidth/2, centerY - shieldHeight/4);
            ctx.lineTo(centerX + shieldWidth/2, centerY + shieldHeight/4);
            ctx.lineTo(centerX, centerY + shieldHeight/2);
            ctx.lineTo(centerX - shieldWidth/2, centerY + shieldHeight/4);
            ctx.lineTo(centerX - shieldWidth/2, centerY - shieldHeight/4);
            ctx.closePath();
            ctx.fill();
            
            // Eye symbol
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = '#ef4444';
            ctx.beginPath();
            ctx.ellipse(centerX, centerY - size*0.1, size*0.2, size*0.12, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye center
            ctx.fillStyle = '#1e293b';
            ctx.beginPath();
            ctx.arc(centerX, centerY - size*0.1, size*0.08, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye highlight
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(centerX + size*0.02, centerY - size*0.12, size*0.03, 0, 2 * Math.PI);
            ctx.fill();
            
            // Warning dots
            ctx.fillStyle = '#f59e0b';
            ctx.beginPath();
            ctx.arc(centerX - size*0.15, centerY + size*0.15, size*0.04, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(centerX + size*0.15, centerY + size*0.15, size*0.04, 0, 2 * Math.PI);
            ctx.fill();
            
            // Security lock
            ctx.fillStyle = '#10b981';
            ctx.fillRect(centerX - size*0.06, centerY + size*0.2, size*0.12, size*0.08);
        }

        function generateIcons() {
            const iconSizes = [
                { size: 192, name: 'logo192.png', label: 'PWA Icon (192x192)' },
                { size: 512, name: 'logo512.png', label: 'PWA Icon (512x512)' },
                { size: 32, name: 'favicon.ico', label: 'Favicon (32x32)' }
            ];

            const preview = document.getElementById('iconPreview');
            preview.innerHTML = '';

            iconSizes.forEach(({ size, name, label }) => {
                const iconItem = document.createElement('div');
                iconItem.className = 'icon-item';
                
                const canvas = document.createElement('canvas');
                drawFraudIcon(canvas, size);
                
                const labelEl = document.createElement('div');
                labelEl.textContent = label;
                labelEl.style.fontWeight = 'bold';
                labelEl.style.marginBottom = '5px';
                
                const nameEl = document.createElement('div');
                nameEl.textContent = name;
                nameEl.style.fontSize = '12px';
                nameEl.style.color = '#666';
                
                iconItem.appendChild(labelEl);
                iconItem.appendChild(canvas);
                iconItem.appendChild(nameEl);
                preview.appendChild(iconItem);
            });

            document.getElementById('successMessage').style.display = 'block';
        }
    </script>
</body>
</html>
