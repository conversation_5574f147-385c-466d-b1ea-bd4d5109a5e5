# 🎨 Icon System - Fraud Detection Platform

A comprehensive, self-contained icon generation system that creates SVG-based React components without requiring external dependencies.

## ✨ Features

- **33 Custom Icons** designed for fraud detection workflows
- **No External Dependencies** - uses only built-in Node.js and React
- **TypeScript Support** with full type definitions
- **Configurable Properties** - size, color, stroke width, etc.
- **Design System Integration** - matches Tailwind CSS and platform colors
- **Accessibility Ready** - proper ARIA labels and semantic markup
- **Tree Shakeable** - import only the icons you need

## 🚀 Quick Start

### 1. Generate Icons

```bash
cd dashboard
node scripts/generate-ui-icons.js
```

### 2. Import and Use

```tsx
import { DashboardIcon, AlertTriangleIcon, SearchIcon } from '../components/icons';

function MyComponent() {
  return (
    <div className="flex items-center space-x-2">
      <DashboardIcon size="lg" color="primary" />
      <SearchIcon size="md" color="muted" />
      <AlertTriangleIcon size={20} color="#ef4444" />
    </div>
  );
}
```

## 📦 Available Icons

### Navigation (6 icons)
- `DashboardIcon` - Dashboard overview
- `AnalyticsIcon` - Analytics and charts  
- `CasesIcon` - Case management
- `UsersIcon` - Users and people
- `ReportsIcon` - Reports and documents
- `SettingsIcon` - Settings and configuration

### Status (4 icons)
- `AlertTriangleIcon` - Warning alerts
- `CheckCircleIcon` - Success states
- `XCircleIcon` - Error states
- `InfoIcon` - Information

### Actions (7 icons)
- `SearchIcon` - Search functionality
- `FilterIcon` - Filter data
- `RefreshIcon` - Refresh/reload
- `EditIcon` - Edit/modify
- `TrashIcon` - Delete/remove
- `PlusIcon` - Add/create
- `SaveIcon` - Save data

### UI (6 icons)
- `MenuIcon` - Menu/hamburger
- `XIcon` - Close/cancel
- `ChevronDownIcon` - Dropdown indicator
- `ChevronUpIcon` - Collapse indicator
- `ChevronLeftIcon` - Previous/back
- `ChevronRightIcon` - Next/forward

### Security (6 icons)
- `EyeIcon` - View/monitor
- `EyeOffIcon` - Hide/privacy
- `ShieldIcon` - Security/protection
- `ShieldCheckIcon` - Verified security
- `LockIcon` - Locked/secure
- `UnlockIcon` - Unlocked/accessible

### Data (4 icons)
- `BarChartIcon` - Bar charts
- `TrendingUpIcon` - Growth/increase
- `TrendingDownIcon` - Decline/decrease
- `ActivityIcon` - Activity/pulse

## 🎛️ Icon Properties

```tsx
interface IconProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'muted' | string;
  strokeWidth?: number;
  className?: string;
  'aria-label'?: string;
  title?: string;
  onClick?: () => void;
  [key: string]: any; // Additional SVG props
}
```

### Size Options
- `xs`: 12px
- `sm`: 16px  
- `md`: 20px (default)
- `lg`: 24px
- `xl`: 32px
- Custom: Any number for pixel size

### Color Options
- `primary`: Blue (#3b82f6)
- `secondary`: Gray (#64748b)
- `success`: Green (#10b981)
- `warning`: Amber (#f59e0b)
- `danger`: Red (#ef4444)
- `muted`: Light gray (#64748b)
- Custom: Any CSS color value

## 💡 Usage Examples

### Basic Icons
```tsx
<DashboardIcon />
<AlertTriangleIcon size="lg" />
<SearchIcon color="primary" />
```

### Interactive Icons
```tsx
<RefreshIcon 
  size="md"
  onClick={handleRefresh}
  className="cursor-pointer hover:text-blue-600 transition-colors"
/>
```

### Status Indicators
```tsx
{status === 'success' ? (
  <CheckCircleIcon color="success" />
) : (
  <AlertTriangleIcon color="warning" />
)}
```

### Navigation
```tsx
<nav className="flex space-x-4">
  <DashboardIcon size="sm" />
  <AnalyticsIcon size="sm" />
  <CasesIcon size="sm" />
</nav>
```

### Loading States
```tsx
{isLoading ? (
  <RefreshIcon size="sm" className="animate-spin" />
) : (
  <CheckCircleIcon size="sm" color="success" />
)}
```

## 🛠️ Customization

### Adding New Icons

1. Edit `scripts/generate-ui-icons.js`
2. Add to `ICON_DEFINITIONS` array:

```javascript
{
  name: 'MyNewIcon',
  category: 'actions',
  description: 'My new icon description',
  viewBox: '0 0 24 24',
  type: 'stroke',
  content: `
    <path d="..." />
    <circle cx="12" cy="12" r="3" />
  `
}
```

3. Regenerate: `node scripts/generate-ui-icons.js`

### Custom Styling

```tsx
<DashboardIcon 
  size={28}
  color="#custom-color"
  strokeWidth={1.5}
  className="drop-shadow-lg"
  style={{ filter: 'drop-shadow(0 2px 4px rgba(0,0,0,0.1))' }}
/>
```

## 📁 File Structure

```
dashboard/
├── scripts/
│   ├── generate-ui-icons.js     # Icon generator
│   ├── generate-ui-icons.ts     # TypeScript version
│   └── README.md                # Generator docs
├── src/
│   ├── components/
│   │   ├── icons/               # Generated icons
│   │   │   ├── DashboardIcon.tsx
│   │   │   ├── AlertTriangleIcon.tsx
│   │   │   ├── ...
│   │   │   └── index.ts         # Exports
│   │   └── IconDemo.tsx         # Demo component
│   └── types/
│       └── icons.ts             # TypeScript types
└── ICON_SYSTEM.md               # This file
```

## 🎯 Design Principles

- **Consistency**: All icons follow the same visual style
- **Clarity**: Simple, recognizable shapes
- **Scalability**: Vector-based for crisp rendering at any size
- **Accessibility**: Proper semantic markup and labels
- **Performance**: Minimal bundle impact with tree shaking

## 🔧 Technical Details

- **Format**: SVG with stroke-based design
- **ViewBox**: 24x24 coordinate system
- **Stroke Width**: Configurable (default: 2px)
- **Bundle Size**: ~2KB for all icons (gzipped)
- **Browser Support**: All modern browsers

## 🧪 Testing

View all icons in action:

```tsx
import IconDemo from '../components/IconDemo';

// Render the demo component to see all icons
<IconDemo />
```

## 📝 License

Part of the fraud detection platform. Same licensing terms apply.
