# Fraud Detection Platform - Simplified Local Development Configuration

# Database Configuration (SQLite for local development)
DATABASE_URL=sqlite:///./fraud.db

# API Configuration
RISK_THRESHOLD=0.7
LOG_LEVEL=INFO

# Dashboard Configuration
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
GENERATE_SOURCEMAP=false
SKIP_PREFLIGHT_CHECK=true

# Security Configuration
JWT_SECRET_KEY=your-secret-key-change-in-production
JWT_ALGORITHM=HS256
JWT_EXPIRATION_HOURS=24

# Development Settings
DEBUG=true
DEVELOPMENT_MODE=true
HOT_RELOAD=true
