/**
 * Validation utilities for forms and data
 */

export interface ValidationRule {
  required?: boolean;
  minLength?: number;
  maxLength?: number;
  min?: number;
  max?: number;
  pattern?: RegExp;
  custom?: (value: any) => string | null;
}

export interface ValidationSchema {
  [field: string]: ValidationRule;
}

export interface ValidationResult {
  isValid: boolean;
  errors: Record<string, string>;
}

/**
 * Validate a single field against a rule
 */
export const validateField = (value: any, rule: ValidationRule, fieldName: string): string | null => {
  // Required validation
  if (rule.required && (value === null || value === undefined || value === '')) {
    return `${fieldName} is required`;
  }

  // Skip other validations if value is empty and not required
  if (!rule.required && (value === null || value === undefined || value === '')) {
    return null;
  }

  // String validations
  if (typeof value === 'string') {
    if (rule.minLength && value.length < rule.minLength) {
      return `${fieldName} must be at least ${rule.minLength} characters`;
    }

    if (rule.maxLength && value.length > rule.maxLength) {
      return `${fieldName} must be no more than ${rule.maxLength} characters`;
    }

    if (rule.pattern && !rule.pattern.test(value)) {
      return `${fieldName} format is invalid`;
    }
  }

  // Number validations
  if (typeof value === 'number') {
    if (rule.min !== undefined && value < rule.min) {
      return `${fieldName} must be at least ${rule.min}`;
    }

    if (rule.max !== undefined && value > rule.max) {
      return `${fieldName} must be no more than ${rule.max}`;
    }
  }

  // Custom validation
  if (rule.custom) {
    return rule.custom(value);
  }

  return null;
};

/**
 * Validate an object against a schema
 */
export const validateSchema = (data: Record<string, any>, schema: ValidationSchema): ValidationResult => {
  const errors: Record<string, string> = {};

  for (const [field, rule] of Object.entries(schema)) {
    const error = validateField(data[field], rule, field);
    if (error) {
      errors[field] = error;
    }
  }

  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Common validation patterns
 */
export const patterns = {
  email: /^[^\s@]+@[^\s@]+\.[^\s@]+$/,
  phone: /^\+?[\d\s\-\(\)]+$/,
  url: /^https?:\/\/.+/,
  alphanumeric: /^[a-zA-Z0-9]+$/,
  numeric: /^\d+$/,
  decimal: /^\d+(\.\d+)?$/,
  password: /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$/,
  transactionId: /^[A-Za-z0-9_-]+$/,
  accountNumber: /^[A-Za-z0-9]+$/
};

/**
 * Pre-defined validation rules
 */
export const rules = {
  required: { required: true },
  email: {
    required: true,
    pattern: patterns.email
  },
  password: {
    required: true,
    minLength: 8,
    pattern: patterns.password
  },
  phone: {
    pattern: patterns.phone
  },
  url: {
    pattern: patterns.url
  },
  transactionId: {
    required: true,
    pattern: patterns.transactionId,
    minLength: 1,
    maxLength: 50
  },
  amount: {
    required: true,
    min: 0,
    custom: (value: any) => {
      if (typeof value !== 'number' || isNaN(value)) {
        return 'Amount must be a valid number';
      }
      return null;
    }
  },
  riskScore: {
    min: 0,
    max: 1,
    custom: (value: any) => {
      if (value !== undefined && (typeof value !== 'number' || isNaN(value))) {
        return 'Risk score must be a valid number';
      }
      return null;
    }
  }
};

/**
 * Validation schemas for common forms
 */
export const schemas = {
  login: {
    username: rules.required,
    password: rules.required
  },

  case: {
    transaction_id: rules.transactionId,
    tag: rules.required,
    comment: { maxLength: 500 }
  },

  transaction: {
    transaction_id: rules.transactionId,
    type: rules.required,
    amount: rules.amount,
    nameOrig: rules.required,
    nameDest: rules.required
  },

  user: {
    username: {
      required: true,
      minLength: 3,
      maxLength: 50,
      pattern: patterns.alphanumeric
    },
    password: rules.password,
    role: rules.required
  }
};

/**
 * Sanitize input data
 */
export const sanitize = {
  /**
   * Remove HTML tags and dangerous characters
   */
  html: (input: string): string => {
    if (!input) return '';
    return input
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/[<>'"&]/g, '') // Remove dangerous characters
      .trim();
  },

  /**
   * Sanitize for SQL-like queries (basic protection)
   */
  sql: (input: string): string => {
    if (!input) return '';
    return input
      .replace(/[';-]/g, '') // Remove SQL injection patterns
      .replace(/--/g, '') // Remove SQL comment patterns
      .trim();
  },

  /**
   * Sanitize numeric input
   */
  number: (input: any): number | null => {
    if (input === null || input === undefined || input === '') {
      return null;
    }

    const num = Number(input);
    return isNaN(num) ? null : num;
  },

  /**
   * Sanitize boolean input
   */
  boolean: (input: any): boolean => {
    if (typeof input === 'boolean') return input;
    if (typeof input === 'string') {
      return input.toLowerCase() === 'true';
    }
    return Boolean(input);
  },

  /**
   * Sanitize array input
   */
  array: (input: any): any[] => {
    if (Array.isArray(input)) return input;
    if (input === null || input === undefined) return [];
    return [input];
  }
};

/**
 * Data type validators
 */
export const isValid = {
  email: (email: string): boolean => patterns.email.test(email),
  phone: (phone: string): boolean => patterns.phone.test(phone),
  url: (url: string): boolean => patterns.url.test(url),
  date: (date: string): boolean => !isNaN(Date.parse(date)),
  json: (json: string): boolean => {
    try {
      JSON.parse(json);
      return true;
    } catch {
      return false;
    }
  },
  uuid: (uuid: string): boolean => {
    const uuidPattern = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidPattern.test(uuid);
  },
  creditCard: (cardNumber: string): boolean => {
    // Luhn algorithm for credit card validation
    const cleaned = cardNumber.replace(/\D/g, '');
    if (cleaned.length < 13 || cleaned.length > 19) return false;

    let sum = 0;
    let isEven = false;

    for (let i = cleaned.length - 1; i >= 0; i--) {
      let digit = parseInt(cleaned[i]);

      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit -= 9;
        }
      }

      sum += digit;
      isEven = !isEven;
    }

    return sum % 10 === 0;
  }
};

/**
 * Form validation hook
 */
import { useState, useCallback } from 'react';

export function useValidation(schema: ValidationSchema) {
  const [errors, setErrors] = useState<Record<string, string>>({});

  const validate = useCallback((data: Record<string, any>): boolean => {
    const result = validateSchema(data, schema);
    setErrors(result.errors);
    return result.isValid;
  }, [schema]);

  const validateSingleField = useCallback((field: string, value: any): boolean => {
    const rule = schema[field];
    if (!rule) return true;

    const error = validateField(value, rule);
    setErrors(prev => ({
      ...prev,
      [field]: error || ''
    }));

    return !error;
  }, [schema]);

  const clearErrors = useCallback(() => {
    setErrors({});
  }, []);

  const clearFieldError = useCallback((field: string) => {
    setErrors(prev => {
      const newErrors = { ...prev };
      delete newErrors[field];
      return newErrors;
    });
  }, []);

  return {
    errors,
    validate,
    validateField: validateSingleField,
    clearErrors,
    clearFieldError,
    hasErrors: Object.keys(errors).length > 0
  };
}
