#!/usr/bin/env python3
"""
Fraud Detection Platform - Enhanced Setup Script
This script installs all dependencies and sets up the platform for development.
Optimized for 3-4 command setup process with robust dependency management.
"""

import sys
import subprocess
import platform
import shutil
from pathlib import Path


class PlatformSetup:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.python_executable = sys.executable
        self.setup_log = []

    def log_step(self, step_name, success, details=""):
        """Log setup steps for debugging"""
        status = "✅" if success else "❌"
        message = f"{status} {step_name}"
        if details:
            message += f": {details}"
        print(message)
        self.setup_log.append((step_name, success, details))

    def check_prerequisites(self):
        """Check if required tools are installed with enhanced validation"""
        print("🔍 Checking prerequisites...")

        # Check Python version
        if sys.version_info < (3, 11):
            error_msg = (f"Python 3.11+ required, "
                        f"found {sys.version}")
            self.log_step("Python Version", False, error_msg)
            print("   💡 Please install Python 3.11+ from "
                  "https://python.org/")
            return False
        version_info = (f"Python {sys.version_info.major}."
                       f"{sys.version_info.minor}")
        self.log_step("Python Version", True, version_info)

        # Check Node.js with version validation
        try:
            result = subprocess.run(['node', '--version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                # Extract version number (e.g., v18.17.0 -> 18)
                major_version = int(version.lstrip('v').split('.')[0])
                if major_version >= 16:
                    self.log_step("Node.js", True, f"{version}")
                else:
                    error_msg = f"{version} (requires v16+)"
                    self.log_step("Node.js", False, error_msg)
                    return False
            else:
                self.log_step("Node.js", False, "Command failed")
                return False
        except (FileNotFoundError, subprocess.TimeoutExpired, ValueError):
            self.log_step("Node.js", False, "Not found or invalid")
            print("   💡 Please install Node.js 16+ from "
                  "https://nodejs.org/")
            return False

        # Check npm with enhanced validation
        try:
            result = subprocess.run(['npm', '--version'],
                                  capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                version = result.stdout.strip()
                self.log_step("npm", True, f"v{version}")
            else:
                self.log_step("npm", False, "Command failed")
                return False
        except (FileNotFoundError, subprocess.TimeoutExpired):
            self.log_step("npm", False, "Not found")
            print("   💡 npm should be installed with Node.js")
            return False

        # Check available disk space (minimum 2GB)
        try:
            free_space_gb = (shutil.disk_usage(self.project_root).free /
                           (1024**3))
            if free_space_gb < 2:
                error_msg = (f"Only {free_space_gb:.1f}GB available "
                           f"(need 2GB+)")
                self.log_step("Disk Space", False, error_msg)
                return False
            self.log_step("Disk Space", True,
                         f"{free_space_gb:.1f}GB available")
        except Exception:
            self.log_step("Disk Space", True, "Unable to check (proceeding)")

        return True

    def setup_python_environment(self):
        """Set up Python virtual environment and install dependencies"""
        print("\n🐍 Setting up Python environment...")

        venv_path = self.project_root / 'venv'

        # Create virtual environment if it doesn't exist
        if not venv_path.exists():
            self.log_step("Creating Virtual Environment", True, "Starting...")
            try:
                subprocess.run([self.python_executable, '-m', 'venv', 'venv'],
                             cwd=self.project_root, check=True, timeout=60)
                self.log_step("Virtual Environment", True, "Created successfully")
            except subprocess.CalledProcessError as e:
                self.log_step("Virtual Environment", False, f"Creation failed: {e}")
                return None
            except subprocess.TimeoutExpired:
                self.log_step("Virtual Environment", False, "Creation timed out")
                return None
        else:
            self.log_step("Virtual Environment", True, "Already exists")

        # Get pip and python paths
        if platform.system() == 'Windows':
            pip_path = venv_path / 'Scripts' / 'pip.exe'
            python_path = venv_path / 'Scripts' / 'python.exe'
        else:
            pip_path = venv_path / 'bin' / 'pip'
            python_path = venv_path / 'bin' / 'python'

        # Verify paths exist
        if not python_path.exists():
            self.log_step("Python Executable", False, f"Not found at {python_path}")
            return None
        if not pip_path.exists():
            self.log_step("Pip Executable", False, f"Not found at {pip_path}")
            return None

        # Upgrade pip with timeout and error handling
        try:
            self.log_step("Upgrading pip", True, "Starting...")
            subprocess.run([str(python_path), '-m', 'pip', 'install',
                          '--upgrade', 'pip'], check=True, timeout=120,
                         capture_output=True)
            self.log_step("Pip Upgrade", True, "Completed")
        except subprocess.CalledProcessError as e:
            self.log_step("Pip Upgrade", False, f"Failed: {e}")
            # Continue anyway, might still work
        except subprocess.TimeoutExpired:
            self.log_step("Pip Upgrade", False, "Timed out")
            return None

        # Install dependencies with enhanced error handling
        requirements_path = self.project_root / 'requirements.txt'
        if not requirements_path.exists():
            self.log_step("Requirements File", False, "requirements.txt not found")
            return None

        try:
            self.log_step("Installing Dependencies", True, "Starting...")
            result = subprocess.run([str(pip_path), 'install', '-r',
                                   str(requirements_path)],
                                  check=True, timeout=300,
                                  capture_output=True, text=True)
            self.log_step("Python Dependencies", True, "All installed successfully")
        except subprocess.CalledProcessError as e:
            error_msg = f"Installation failed: {e}"
            if e.stderr:
                error_msg += f"\nError output: {e.stderr[:200]}..."
            self.log_step("Python Dependencies", False, error_msg)
            return None
        except subprocess.TimeoutExpired:
            self.log_step("Python Dependencies", False, "Installation timed out")
            return None

        return str(python_path)

    def setup_frontend(self):
        """Set up React frontend dependencies using npm package manager"""
        print("\n🌐 Setting up React frontend...")

        dashboard_path = self.project_root / 'dashboard'

        # Verify dashboard directory exists
        if not dashboard_path.exists():
            self.log_step("Dashboard Directory", False, "Directory not found")
            return False

        # Verify package.json exists
        package_json_path = dashboard_path / 'package.json'
        if not package_json_path.exists():
            self.log_step("Package.json", False, "File not found")
            return False

        # Clean npm cache if node_modules exists but is corrupted
        node_modules_path = dashboard_path / 'node_modules'
        if node_modules_path.exists():
            try:
                # Quick check if node_modules is healthy
                result = subprocess.run(['npm', 'list'], cwd=dashboard_path,
                                      capture_output=True, timeout=30)
                if result.returncode != 0:
                    self.log_step("Cleaning npm cache", True, "Corrupted modules detected")
                    subprocess.run(['npm', 'cache', 'clean', '--force'],
                                 cwd=dashboard_path, check=True, timeout=60)
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                self.log_step("npm cache clean", False, "Failed, continuing anyway")

        # Install npm dependencies with enhanced error handling
        try:
            self.log_step("Installing npm dependencies", True, "Starting...")

            # Use npm ci if package-lock.json exists for faster, reliable installs
            package_lock_path = dashboard_path / 'package-lock.json'
            if package_lock_path.exists():
                cmd = ['npm', 'ci']
                install_type = "npm ci (clean install)"
            else:
                cmd = ['npm', 'install']
                install_type = "npm install"

            result = subprocess.run(cmd, cwd=dashboard_path, check=True,
                                  timeout=600, capture_output=True, text=True)
            self.log_step("npm Dependencies", True,
                         f"{install_type} completed successfully")

        except subprocess.CalledProcessError as e:
            error_msg = f"npm install failed: {e}"
            if e.stderr:
                error_msg += f"\nError output: {e.stderr[:200]}..."
            self.log_step("npm Dependencies", False, error_msg)

            # Try alternative: npm install --legacy-peer-deps
            try:
                self.log_step("Retrying with legacy-peer-deps", True, "Starting...")
                subprocess.run(['npm', 'install', '--legacy-peer-deps'],
                             cwd=dashboard_path, check=True, timeout=600,
                             capture_output=True)
                self.log_step("npm Dependencies", True,
                             "Installed with legacy-peer-deps")
            except subprocess.CalledProcessError:
                self.log_step("npm Dependencies", False,
                             "All installation methods failed")
                return False

        except subprocess.TimeoutExpired:
            self.log_step("npm Dependencies", False, "Installation timed out")
            return False

        # Verify installation success
        try:
            result = subprocess.run(['npm', 'list', '--depth=0'],
                                  cwd=dashboard_path, capture_output=True,
                                  timeout=30)
            if result.returncode == 0:
                self.log_step("Dependency Verification", True,
                             "All packages installed correctly")
            else:
                self.log_step("Dependency Verification", False,
                             "Some packages may have issues")
        except subprocess.TimeoutExpired:
            self.log_step("Dependency Verification", False, "Verification timed out")

        return True

    def setup_database_and_config(self):
        """Initialize database and configuration files"""
        print("\n�️ Setting up database and configuration...")

        # Verify fraud-api service exists
        fraud_api_path = self.project_root / 'fraud-api'
        if not fraud_api_path.exists():
            self.log_step("Fraud API Directory", False, "Directory not found")
            return False

        fraud_api_main = fraud_api_path / 'main.py'
        if not fraud_api_main.exists():
            self.log_step("Fraud API Service", False, "main.py not found")
            return False

        self.log_step("Fraud API Service", True, "Service files verified")

        # Create/verify SQLite database file
        db_path = self.project_root / 'fraud.db'
        try:
            if not db_path.exists():
                db_path.touch()
                self.log_step("Database File", True, "Created fraud.db")
            else:
                self.log_step("Database File", True, "fraud.db already exists")
        except Exception as e:
            self.log_step("Database File", False, f"Failed to create: {e}")
            return False

        # Verify essential configuration files exist
        essential_files = [
            ('requirements.txt', 'Python dependencies'),
            ('dashboard/package.json', 'Frontend dependencies'),
            ('fraud-api/__init__.py', 'API module init'),
        ]

        for file_path, description in essential_files:
            full_path = self.project_root / file_path
            if full_path.exists():
                self.log_step(description, True, f"{file_path} found")
            else:
                self.log_step(description, False, f"{file_path} missing")
                return False

        return True

    def create_startup_validation(self):
        """Create validation script for testing setup"""
        print("\n🧪 Creating startup validation...")

        # Check if test file exists
        test_file = self.project_root / 'test_simplified_platform.py'
        if test_file.exists():
            self.log_step("Test Script", True, "test_simplified_platform.py found")
            return True
        else:
            self.log_step("Test Script", False, "test_simplified_platform.py missing")
            return False

    def run_setup(self):
        """Run the complete setup process"""
        print("🚀 Starting Fraud Detection Platform Setup")
        print("=" * 50)

        try:
            # Check prerequisites
            if not self.check_prerequisites():
                print("\n❌ Setup failed: Missing prerequisites")
                return False

            # Setup Python environment
            self.setup_python_environment()

            # Setup frontend
            self.setup_frontend()

            # Create fraud API service
            self.create_fraud_api_service()

            # Initialize database
            self.initialize_database()

            print("\n" + "=" * 50)
            print("🎉 Setup completed successfully!")
            print("=" * 50)
            print("\n📋 Next steps:")
            print("1. Run: python start.py")
            print("2. Open: http://localhost:3000")
            print("3. Login with: analyst / password")
            print("\n💡 Tip: Use 'python start.py --help' for more options")

            return True

        except subprocess.CalledProcessError as e:
            print(f"\n❌ Setup failed: {e}")
            return False
        except Exception as e:
            print(f"\n❌ Unexpected error: {e}")
            return False


def main():
    setup = PlatformSetup()
    success = setup.run_setup()
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
