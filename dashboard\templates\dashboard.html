--analyst / password--

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FraudShield React | Advanced Fraud Detection</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.5; }
        }
        .pulse {
            animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
        }
        .risk-gradient-low {
            background: linear-gradient(135deg, #d1fae5 0%, #a7f3d0 100%);
        }
        .risk-gradient-medium {
            background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
        }
        .risk-gradient-high {
            background: linear-gradient(135deg, #fee2e2 0%, #fca5a5 100%);
        }
        .tooltip:hover .tooltip-text {
            visibility: visible;
            opacity: 1;
        }
        .history-item {
            transition: all 0.2s ease;
        }
        .history-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        }
    </style>
</head>
<body>
    <div id="root"></div>

    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>

    <script type="text/babel">
        // Main App Component
        function App() {
            const [darkMode, setDarkMode] = React.useState(localStorage.getItem('darkMode') === 'true');
            const [currentTime, setCurrentTime] = React.useState(new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
            const [modelStatus, setModelStatus] = React.useState({ status: 'checking', message: 'Model Service: Checking...' });
            const [ingestStatus, setIngestStatus] = React.useState({ status: 'checking', message: 'Ingest Service: Checking...' });
            const [history, setHistory] = React.useState(JSON.parse(localStorage.getItem('fraudDetectionHistory')) || []);
            const [formData, setFormData] = React.useState({
                type: 'PAYMENT',
                amount: '1000',
                nameOrig: 'C123456789',
                oldbalanceOrg: '5000',
                newbalanceOrig: '4000',
                nameDest: 'M987654321',
                oldbalanceDest: '0',
                newbalanceDest: '1000'
            });
            const [validationErrors, setValidationErrors] = React.useState({
                nameOrig: false,
                nameDest: false
            });
            const [result, setResult] = React.useState(null);
            const [loading, setLoading] = React.useState(false);

            // Configuration
            const MODEL_SERVICE_URL = 'http://localhost:8001';
            const INGEST_SERVICE_URL = 'http://localhost:9001';
            const HISTORY_KEY = 'fraudDetectionHistory';

            // Initialize
            React.useEffect(() => {
                checkServiceStatus();
                updateClock();
                const clockInterval = setInterval(updateClock, 1000);
                const statusInterval = setInterval(checkServiceStatus, 30000);

                return () => {
                    clearInterval(clockInterval);
                    clearInterval(statusInterval);
                };
            }, []);

            // Update dark mode
            React.useEffect(() => {
                localStorage.setItem('darkMode', darkMode);
            }, [darkMode]);

            // Update history in localStorage
            React.useEffect(() => {
                localStorage.setItem(HISTORY_KEY, JSON.stringify(history));
            }, [history]);

            // Clock update
            function updateClock() {
                setCurrentTime(new Date().toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' }));
            }

            // Toggle dark mode
            function toggleDarkMode() {
                setDarkMode(!darkMode);
            }

            // Check service status
            async function checkServiceStatus() {
                try {
                    const response = await fetch(`${MODEL_SERVICE_URL}/health`);
                    if (response.ok) {
                        setModelStatus({
                            status: 'online',
                            message: 'Model Service: Online'
                        });
                    } else {
                        throw new Error('Service unavailable');
                    }
                } catch (error) {
                    setModelStatus({
                        status: 'offline',
                        message: 'Model Service: Offline'
                    });
                }

                try {
                    const response = await fetch(`${INGEST_SERVICE_URL}/health`);
                    if (response.ok) {
                        setIngestStatus({
                            status: 'online',
                            message: 'Ingest Service: Online'
                        });
                    } else {
                        throw new Error('Service unavailable');
                    }
                } catch (error) {
                    setIngestStatus({
                        status: 'offline',
                        message: 'Ingest Service: Offline (using Model Service directly)'
                    });
                }
            }

            // Handle form input changes
            function handleInputChange(e) {
                const { name, value } = e.target;
                setFormData(prev => ({
                    ...prev,
                    [name]: value
                }));

                // Validate inputs
                if (name === 'nameOrig') {
                    const isValid = /^C\d{9}$/.test(value);
                    setValidationErrors(prev => ({
                        ...prev,
                        nameOrig: !isValid
                    }));
                } else if (name === 'nameDest') {
                    const isValid = /^(M|C)\d{9}$/.test(value);
                    setValidationErrors(prev => ({
                        ...prev,
                        nameDest: !isValid
                    }));
                }
            }

            // Generate random transaction
            function generateRandomTransaction() {
                const types = ['PAYMENT', 'TRANSFER', 'CASH_OUT', 'DEBIT'];
                const randomType = types[Math.floor(Math.random() * types.length)];
                const amount = (Math.floor(Math.random() * 10000) + 10).toFixed(2);
                const oldBalanceOrg = (Math.floor(Math.random() * 20000) + parseFloat(amount)).toFixed(2);
                const newBalanceOrg = (oldBalanceOrg - amount).toFixed(2);
                const oldBalanceDest = Math.floor(Math.random() * 5000).toFixed(2);
                const newBalanceDest = (parseFloat(oldBalanceDest) + parseFloat(amount)).toFixed(2);

                setFormData({
                    type: randomType,
                    amount,
                    nameOrig: `C${Math.floor(100000000 + Math.random() * 900000000)}`,
                    oldbalanceOrg: oldBalanceOrg,
                    newbalanceOrig: newBalanceOrg,
                    nameDest: `M${Math.floor(100000000 + Math.random() * 900000000)}`,
                    oldbalanceDest: oldBalanceDest,
                    newbalanceDest: newBalanceDest
                });

                // Reset validation errors
                setValidationErrors({
                    nameOrig: false,
                    nameDest: false
                });
            }

            // Handle form submission
            async function handleSubmit(e) {
                e.preventDefault();

                // Validate form
                if (validationErrors.nameOrig || validationErrors.nameDest) {
                    setResult({
                        type: 'error',
                        message: 'Please correct the highlighted fields before submitting.'
                    });
                    return;
                }

                setLoading(true);
                setResult(null);

                const transaction = {
                    transaction_id: `test_${Date.now()}`,
                    step: 1,
                    type: formData.type,
                    amount: parseFloat(formData.amount),
                    nameOrig: formData.nameOrig,
                    oldbalanceOrg: parseFloat(formData.oldbalanceOrg),
                    newbalanceOrig: parseFloat(formData.newbalanceOrig),
                    nameDest: formData.nameDest,
                    oldbalanceDest: parseFloat(formData.oldbalanceDest),
                    newbalanceDest: parseFloat(formData.newbalanceDest)
                };

                try {
                    const response = await fetch(`${MODEL_SERVICE_URL}/score`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                        },
                        body: JSON.stringify({ transactions: [transaction] })
                    });

                    if (response.ok) {
                        const resultData = await response.json();
                        const riskScore = resultData.results[0].risk;
                        setResult({
                            type: 'success',
                            transaction,
                            riskScore
                        });
                        updateHistory(transaction, { risk: riskScore });
                    } else {
                        throw new Error(`HTTP ${response.status}`);
                    }
                } catch (error) {
                    console.error('Error:', error);
                    setResult({
                        type: 'error',
                        message: `Failed to score transaction: ${error.message}`
                    });
                } finally {
                    setLoading(false);
                }
            }

            // Update history
            function updateHistory(transaction, resultData) {
                const newHistory = [{
                    transaction,
                    result: resultData,
                    timestamp: new Date()
                }, ...history].slice(0, 5);
                setHistory(newHistory);
            }

            // Clear history
            function clearHistory() {
                if (window.confirm('Are you sure you want to clear your test history?')) {
                    setHistory([]);
                }
            }

            // Fill form from history
            function fillFormFromHistory(item) {
                setFormData({
                    type: item.transaction.type,
                    amount: item.transaction.amount.toString(),
                    nameOrig: item.transaction.nameOrig,
                    oldbalanceOrg: item.transaction.oldbalanceOrg.toString(),
                    newbalanceOrig: item.transaction.newbalanceOrig.toString(),
                    nameDest: item.transaction.nameDest,
                    oldbalanceDest: item.transaction.oldbalanceDest.toString(),
                    newbalanceDest: item.transaction.newbalanceDest.toString()
                });

                // Reset validation errors
                setValidationErrors({
                    nameOrig: false,
                    nameDest: false
                });

                // Scroll to form
                document.getElementById('transactionForm').scrollIntoView({ behavior: 'smooth' });
            }

            // Copy to clipboard
            function copyToClipboard(text) {
                navigator.clipboard.writeText(text).then(() => {
                    // Show feedback
                    const originalText = document.getElementById('btnText').textContent;
                    document.getElementById('btnText').textContent = 'Copied!';
                    setTimeout(() => {
                        document.getElementById('btnText').textContent = originalText;
                    }, 2000);
                });
            }

            return (
                <div className={`min-h-screen ${darkMode ? 'bg-gray-900 text-gray-100' : 'bg-gray-50'}`}>
                    <div className="container mx-auto px-4 py-8 max-w-6xl">
                        {/* Header */}
                        <header className="bg-gradient-to-r from-blue-600 to-indigo-800 text-white rounded-xl p-6 mb-8 shadow-lg">
                            <div className="flex items-center justify-between">
                                <div>
                                    <h1 className="text-3xl font-bold flex items-center">
                                        <i className="fas fa-shield-alt mr-3"></i>FraudShield
                                    </h1>
                                    <p className="mt-2 opacity-90">Advanced real-time transaction fraud detection system</p>
                                </div>
                                <div className="flex items-center space-x-3">
                                    <span className="text-sm opacity-80">{currentTime}</span>
                                    <button 
                                        onClick={toggleDarkMode}
                                        className="bg-white bg-opacity-10 hover:bg-opacity-20 p-2 rounded-full transition"
                                    >
                                        <i className={darkMode ? 'fas fa-sun' : 'fas fa-moon'}></i>
                                    </button>
                                </div>
                            </div>
                        </header>

                        {/* Service Status */}
                        <div className={`rounded-xl p-6 mb-8 shadow-md ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                            <h2 className="text-xl font-semibold mb-4 flex items-center">
                                <i className="fas fa-server mr-2 text-blue-600"></i> Service Status
                                <button 
                                    onClick={checkServiceStatus}
                                    className="ml-auto text-sm bg-gray-100 hover:bg-gray-200 px-3 py-1 rounded-md"
                                >
                                    <i className="fas fa-sync-alt mr-1"></i> Refresh
                                </button>
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                <div className={`px-4 py-3 rounded-lg flex items-center ${
                                    modelStatus.status === 'online' ? 'bg-green-50 text-green-800 border border-green-200' : 
                                    modelStatus.status === 'offline' ? 'bg-red-50 text-red-800 border border-red-200' : 
                                    'bg-gray-100 text-gray-800'
                                }`}>
                                    <i className={`fas ${
                                        modelStatus.status === 'online' ? 'fa-check-circle text-green-500' : 
                                        modelStatus.status === 'offline' ? 'fa-times-circle text-red-500' : 
                                        'fa-circle-notch fa-spin'
                                    } mr-2`}></i>
                                    <span>{modelStatus.message}</span>
                                </div>
                                <div className={`px-4 py-3 rounded-lg flex items-center ${
                                    ingestStatus.status === 'online' ? 'bg-green-50 text-green-800 border border-green-200' : 
                                    ingestStatus.status === 'offline' ? 'bg-red-50 text-red-800 border border-red-200' : 
                                    'bg-gray-100 text-gray-800'
                                }`}>
                                    <i className={`fas ${
                                        ingestStatus.status === 'online' ? 'fa-check-circle text-green-500' : 
                                        ingestStatus.status === 'offline' ? 'fa-times-circle text-red-500' : 
                                        'fa-circle-notch fa-spin'
                                    } mr-2`}></i>
                                    <span>{ingestStatus.message}</span>
                                </div>
                            </div>
                        </div>

                        {/* Transaction Form */}
                        <div className={`rounded-xl p-6 mb-8 shadow-md ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                            <h2 className="text-xl font-semibold mb-4 flex items-center">
                                <i className="fas fa-credit-card mr-2 text-blue-600"></i> Test Transaction
                            </h2>
                            <form id="transactionForm" onSubmit={handleSubmit} className="space-y-4">
                                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                                    {/* Left Column */}
                                    <div className="space-y-4">
                                        <div className="form-group">
                                            <label htmlFor="type" className="block text-sm font-medium text-gray-700 mb-1 flex items-center">
                                                Transaction Type
                                                <div className="tooltip ml-1 relative">
                                                    <i className="fas fa-info-circle text-blue-500 cursor-pointer"></i>
                                                    <div className="tooltip-text absolute z-10 invisible opacity-0 bg-gray-800 text-white text-xs rounded py-1 px-2 -left-20 w-40 mt-2 transition-all">
                                                        Select the type of financial transaction
                                                    </div>
                                                </div>
                                            </label>
                                            <select
                                                id="type"
                                                name="type"
                                                value={formData.type}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            >
                                                <option value="PAYMENT">Payment</option>
                                                <option value="TRANSFER">Transfer</option>
                                                <option value="CASH_OUT">Cash Out</option>
                                                <option value="DEBIT">Debit</option>
                                            </select>
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="amount" className="block text-sm font-medium text-gray-700 mb-1">
                                                Amount ($)
                                            </label>
                                            <div className="relative">
                                                <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                                    <span className="text-gray-500">$</span>
                                                </div>
                                                <input
                                                    type="number"
                                                    id="amount"
                                                    name="amount"
                                                    step="0.01"
                                                    min="0"
                                                    value={formData.amount}
                                                    onChange={handleInputChange}
                                                    className="pl-7 w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                    required
                                                />
                                            </div>
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="nameOrig" className="block text-sm font-medium text-gray-700 mb-1">
                                                Origin Account
                                            </label>
                                            <input
                                                type="text"
                                                id="nameOrig"
                                                name="nameOrig"
                                                value={formData.nameOrig}
                                                onChange={handleInputChange}
                                                pattern="^C\d{9}$"
                                                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                                                    validationErrors.nameOrig ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                                required
                                            />
                                            {validationErrors.nameOrig && (
                                                <p className="text-xs text-red-600 mt-1">
                                                    Must start with 'C' followed by 9 digits
                                                </p>
                                            )}
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="oldbalanceOrg" className="block text-sm font-medium text-gray-700 mb-1">
                                                Old Balance (Origin)
                                            </label>
                                            <input
                                                type="number"
                                                id="oldbalanceOrg"
                                                name="oldbalanceOrg"
                                                step="0.01"
                                                min="0"
                                                value={formData.oldbalanceOrg}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        </div>
                                    </div>

                                    {/* Right Column */}
                                    <div className="space-y-4">
                                        <div className="form-group">
                                            <label htmlFor="newbalanceOrig" className="block text-sm font-medium text-gray-700 mb-1">
                                                New Balance (Origin)
                                            </label>
                                            <input
                                                type="number"
                                                id="newbalanceOrig"
                                                name="newbalanceOrig"
                                                step="0.01"
                                                min="0"
                                                value={formData.newbalanceOrig}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="nameDest" className="block text-sm font-medium text-gray-700 mb-1">
                                                Destination Account
                                            </label>
                                            <input
                                                type="text"
                                                id="nameDest"
                                                name="nameDest"
                                                value={formData.nameDest}
                                                onChange={handleInputChange}
                                                pattern="^(M|C)\d{9}$"
                                                className={`w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 ${
                                                    validationErrors.nameDest ? 'border-red-500' : 'border-gray-300'
                                                }`}
                                                required
                                            />
                                            {validationErrors.nameDest && (
                                                <p className="text-xs text-red-600 mt-1">
                                                    Must start with 'M' or 'C' followed by 9 digits
                                                </p>
                                            )}
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="oldbalanceDest" className="block text-sm font-medium text-gray-700 mb-1">
                                                Old Balance (Destination)
                                            </label>
                                            <input
                                                type="number"
                                                id="oldbalanceDest"
                                                name="oldbalanceDest"
                                                step="0.01"
                                                min="0"
                                                value={formData.oldbalanceDest}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        </div>

                                        <div className="form-group">
                                            <label htmlFor="newbalanceDest" className="block text-sm font-medium text-gray-700 mb-1">
                                                New Balance (Destination)
                                            </label>
                                            <input
                                                type="number"
                                                id="newbalanceDest"
                                                name="newbalanceDest"
                                                step="0.01"
                                                min="0"
                                                value={formData.newbalanceDest}
                                                onChange={handleInputChange}
                                                className="w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                                required
                                            />
                                        </div>
                                    </div>
                                </div>

                                <div className="flex justify-between items-center pt-4">
                                    <button
                                        type="button"
                                        onClick={generateRandomTransaction}
                                        className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <i className="fas fa-dice mr-2"></i> Random Transaction
                                    </button>
                                    <button
                                        type="submit"
                                        disabled={loading}
                                        className="px-6 py-3 border border-transparent rounded-md shadow-sm text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                                    >
                                        <span id="btnText">
                                            {loading ? (
                                                <>
                                                    <i className="fas fa-circle-notch fa-spin mr-2"></i> Analyzing...
                                                </>
                                            ) : (
                                                <>
                                                    <i className="fas fa-search mr-2"></i> Check for Fraud
                                                </>
                                            )}
                                        </span>
                                    </button>
                                </div>
                            </form>

                            {/* Result Display */}
                            {result && (
                                <div className="mt-6 transition-all duration-300">
                                    {result.type === 'error' ? (
                                        <div className="p-4 rounded-lg bg-red-50 border border-red-200 text-red-800">
                                            <h3 className="font-medium flex items-center">
                                                <i className="fas fa-exclamation-triangle mr-2"></i> Error
                                            </h3>
                                            <p className="mt-1">{result.message}</p>
                                        </div>
                                    ) : (
                                        <ResultDisplay 
                                            transaction={result.transaction} 
                                            riskScore={result.riskScore} 
                                            copyToClipboard={copyToClipboard}
                                        />
                                    )}
                                </div>
                            )}
                        </div>

                        {/* Transaction History */}
                        <div className={`rounded-xl p-6 mb-8 shadow-md ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                            <div className="flex justify-between items-center mb-4">
                                <h2 className="text-xl font-semibold flex items-center">
                                    <i className="fas fa-history mr-2 text-blue-600"></i> Test History
                                </h2>
                                <div className="flex space-x-2">
                                    <button
                                        onClick={clearHistory}
                                        className="px-3 py-1 text-sm border border-gray-300 rounded-md text-gray-700 bg-white hover:bg-gray-50"
                                    >
                                        <i className="fas fa-trash-alt mr-1"></i> Clear
                                    </button>
                                </div>
                            </div>
                            <div className="space-y-2">
                                {history.length === 0 ? (
                                    <div className="text-center py-4 text-gray-500">
                                        <i className="fas fa-history text-2xl mb-2"></i>
                                        <p>No test history yet</p>
                                    </div>
                                ) : (
                                    history.map((item, index) => (
                                        <HistoryItem 
                                            key={index} 
                                            item={item} 
                                            onClick={() => fillFormFromHistory(item)}
                                            darkMode={darkMode}
                                        />
                                    ))
                                )}
                            </div>
                        </div>

                        {/* Risk Explanation */}
                        <div className={`rounded-xl p-6 shadow-md ${darkMode ? 'bg-gray-800' : 'bg-white'}`}>
                            <h2 className="text-xl font-semibold mb-4 flex items-center">
                                <i className="fas fa-info-circle mr-2 text-blue-600"></i> Risk Score Explanation
                            </h2>
                            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                                <div className="risk-gradient-low p-4 rounded-lg border border-green-200">
                                    <h3 className="font-medium text-green-800 flex items-center">
                                        <i className="fas fa-check-circle mr-2"></i> Low Risk (0-49%)
                                    </h3>
                                    <p className="text-green-700 text-sm mt-1">Transaction appears legitimate with minimal risk indicators.</p>
                                </div>
                                <div className="risk-gradient-medium p-4 rounded-lg border border-yellow-200">
                                    <h3 className="font-medium text-yellow-800 flex items-center">
                                        <i className="fas fa-exclamation-triangle mr-2"></i> Medium Risk (50-79%)
                                    </h3>
                                    <p className="text-yellow-700 text-sm mt-1">Transaction shows some suspicious patterns requiring review.</p>
                                </div>
                                <div className="risk-gradient-high p-4 rounded-lg border border-red-200">
                                    <h3 className="font-medium text-red-800 flex items-center">
                                        <i className="fas fa-exclamation-circle mr-2"></i> High Risk (80-100%)
                                    </h3>
                                    <p className="text-red-700 text-sm mt-1">Highly suspicious transaction likely to be fraudulent.</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            );
        }

        // Result Display Component
        function ResultDisplay({ transaction, riskScore, copyToClipboard }) {
            const percentage = (riskScore * 100).toFixed(1);
            let riskClass, riskIcon, riskText, riskColor;

            if (riskScore >= 0.8) {
                riskClass = 'risk-gradient-high';
                riskIcon = 'fa-exclamation-circle';
                riskText = 'HIGH RISK';
                riskColor = 'red';
            } else if (riskScore >= 0.5) {
                riskClass = 'risk-gradient-medium';
                riskIcon = 'fa-exclamation-triangle';
                riskText = 'MEDIUM RISK';
                riskColor = 'yellow';
            } else {
                riskClass = 'risk-gradient-low';
                riskIcon = 'fa-check-circle';
                riskText = 'LOW RISK';
                riskColor = 'green';
            }

            return (
                <div className={`${riskClass} p-6 rounded-xl border border-${riskColor}-200`}>
                    <div className="flex justify-between items-start">
                        <div>
                            <h3 className={`text-xl font-bold flex items-center text-${riskColor}-800`}>
                                <i className={`fas ${riskIcon} mr-3`}></i> {riskText}
                            </h3>
                            <p className={`mt-1 text-${riskColor}-700`}>
                                Risk Score: <span className="font-bold">{percentage}%</span>
                            </p>
                        </div>
                        <button
                            onClick={() => copyToClipboard(JSON.stringify({ transaction, riskScore }))}
                            className={`px-3 py-1 text-sm border border-${riskColor}-300 rounded-md text-${riskColor}-700 bg-white hover:bg-${riskColor}-50`}
                        >
                            <i className="fas fa-copy mr-1"></i> Copy Data
                        </button>
                    </div>

                    <div className="mt-4 grid grid-cols-1 md:grid-cols-2 gap-4">
                        <div className="bg-white bg-opacity-50 p-3 rounded-lg">
                            <h4 className="font-medium text-gray-700">Transaction Details</h4>
                            <div className="mt-2 space-y-1 text-sm">
                                <p><span className="font-medium">ID:</span> {transaction.transaction_id}</p>
                                <p><span className="font-medium">Type:</span> {transaction.type}</p>
                                <p><span className="font-medium">Amount:</span> ${transaction.amount.toLocaleString()}</p>
                            </div>
                        </div>
                        <div className="bg-white bg-opacity-50 p-3 rounded-lg">
                            <h4 className="font-medium text-gray-700">Account Details</h4>
                            <div className="mt-2 space-y-1 text-sm">
                                <p><span className="font-medium">From:</span> {transaction.nameOrig}</p>
                                <p><span className="font-medium">To:</span> {transaction.nameDest}</p>
                                <p>
                                    <span className="font-medium">Balance Change:</span> $
                                    {(transaction.oldbalanceOrg - transaction.newbalanceOrig).toLocaleString()}
                                </p>
                            </div>
                        </div>
                    </div>

                    <div className="mt-4 flex justify-end">
                        <small className="text-xs opacity-70">
                            Analyzed at {new Date().toLocaleTimeString()}
                        </small>
                    </div>
                </div>
            );
        }

        // History Item Component
        function HistoryItem({ item, onClick, darkMode }) {
            const riskScore = item.result.risk;
            let riskClass, riskIcon, riskColor;

            if (riskScore >= 0.8) {
                riskClass = 'bg-red-50 border-red-200 text-red-800';
                riskIcon = 'fa-exclamation-circle';
                riskColor = 'red';
            } else if (riskScore >= 0.5) {
                riskClass = 'bg-yellow-50 border-yellow-200 text-yellow-800';
                riskIcon = 'fa-exclamation-triangle';
                riskColor = 'yellow';
            } else {
                riskClass = 'bg-green-50 border-green-200 text-green-800';
                riskIcon = 'fa-check-circle';
                riskColor = 'green';
            }

            const percentage = (riskScore * 100).toFixed(1);
            const time = new Date(item.timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' });

            return (
                <div
                    className={`history-item ${riskClass} border rounded-lg p-3 cursor-pointer hover:bg-${riskColor}-100`}
                    onClick={onClick}
                >
                    <div className="flex justify-between items-center">
                        <div className="flex items-center">
                            <i className={`fas ${riskIcon} mr-2 text-${riskColor}-600`}></i>
                            <span className="font-medium">{percentage}%</span>
                        </div>
                        <div className="text-sm">
                            {time} • ${item.transaction.amount.toLocaleString()}
                        </div>
                    </div>
                    <div className="mt-1 text-xs flex justify-between">
                        <span>{item.transaction.type}</span>
                        <span>
                            {item.transaction.nameOrig} → {item.transaction.nameDest}
                        </span>
                    </div>
                </div>
            );
        }

        // Render the app
        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<App />);
    </script>
</body>
</html>