<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="eyeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#shieldGradient)" stroke="#1e40af" stroke-width="2"/>

  <!-- Shield shape -->
  <path d="M32 8 L48 16 L48 32 Q48 48 32 56 Q16 48 16 32 L16 16 Z"
        fill="rgba(255,255,255,0.9)"
        stroke="#1e40af"
        stroke-width="1.5"/>

  <!-- Eye symbol (fraud detection/monitoring) -->
  <ellipse cx="32" cy="28" rx="12" ry="8" fill="url(#eyeGradient)" opacity="0.8"/>
  <ellipse cx="32" cy="28" rx="8" ry="5" fill="rgba(255,255,255,0.3)"/>
  <circle cx="32" cy="28" r="4" fill="#1e293b"/>
  <circle cx="33" cy="27" r="1.5" fill="white"/>

  <!-- Warning/Alert indicators -->
  <circle cx="24" cy="40" r="2" fill="#f59e0b" opacity="0.8"/>
  <circle cx="32" cy="42" r="1.5" fill="#f59e0b" opacity="0.8"/>
  <circle cx="40" cy="40" r="2" fill="#f59e0b" opacity="0.8"/>

  <!-- Security lock accent -->
  <rect x="29" y="45" width="6" height="4" rx="1" fill="rgba(16,185,129,0.6)"/>
  <path d="M30 45 L30 43 Q30 41 32 41 Q34 41 34 43 L34 45"
        fill="none"
        stroke="rgba(16,185,129,0.8)"
        stroke-width="1.5"/>
</svg>