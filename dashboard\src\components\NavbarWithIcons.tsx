import React from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../services/AuthContext';
import { 
  DashboardIcon, 
  CasesIcon, 
  AnalyticsIcon, 
  ShieldIcon,
  UsersIcon 
} from './icons';
import '../styles/Navbar.css';

/**
 * Enhanced Navbar component with icons
 * This demonstrates how to integrate the generated icon system
 */
const NavbarWithIcons = () => {
  const { logout, user } = useAuth();
  const location = useLocation();

  const handleLogout = () => {
    logout();
  };

  const navigationItems = [
    {
      to: '/',
      label: 'Dashboard',
      icon: DashboardIcon,
      isActive: location.pathname === '/'
    },
    {
      to: '/cases',
      label: 'Case Management',
      icon: CasesIcon,
      isActive: location.pathname === '/cases'
    },
    {
      to: '/analytics',
      label: 'Analytics',
      icon: AnalyticsIcon,
      isActive: location.pathname === '/analytics'
    }
  ];

  return (
    <nav className="navbar">
      <div className="navbar-brand">
        <div className="flex items-center space-x-2">
          <ShieldIcon size="lg" color="primary" />
          <h1>Fraud Detection</h1>
        </div>
      </div>
      
      <div className="navbar-menu">
        {navigationItems.map(({ to, label, icon: IconComponent, isActive }) => (
          <Link 
            key={to}
            to={to} 
            className={`navbar-item ${isActive ? 'active' : ''}`}
          >
            <div className="flex items-center space-x-2">
              <IconComponent 
                size="sm" 
                color={isActive ? 'primary' : 'muted'}
                className="transition-colors"
              />
              <span>{label}</span>
            </div>
          </Link>
        ))}
      </div>
      
      <div className="navbar-user">
        {user && (
          <>
            <span className="user-info">
              <div className="flex items-center space-x-2">
                <UsersIcon size="sm" color="muted" />
                <div className="flex flex-col">
                  <span className="username">{user.username}</span>
                  <span className="role">{user.role}</span>
                </div>
              </div>
            </span>
            <button className="logout-button" onClick={handleLogout}>
              Logout
            </button>
          </>
        )}
      </div>
    </nav>
  );
};

export default NavbarWithIcons;
