import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Delete or remove icon
 * Category: actions
 */
export const TrashIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-trash ${className}`}
      aria-label={ariaLabel || 'Delete or remove icon'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <polyline points="3,6 5,6 21,6" />
      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
      <line x1="10" y1="11" x2="10" y2="17" />
      <line x1="14" y1="11" x2="14" y2="17" />
    </svg>
  );
};

export default TrashIcon;
