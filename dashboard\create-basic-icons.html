<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick Icon Creator - Fraud Detection Platform</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            text-align: center;
        }
        h1 {
            color: #3b82f6;
            margin-bottom: 20px;
        }
        .icon-container {
            margin: 20px 0;
            padding: 20px;
            border: 2px dashed #d1d5db;
            border-radius: 8px;
        }
        canvas {
            border: 1px solid #d1d5db;
            margin: 10px;
            background: white;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-size: 14px;
        }
        button:hover {
            background: #2563eb;
        }
        .instructions {
            background: #eff6ff;
            border: 1px solid #bfdbfe;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            text-align: left;
        }
        .step {
            margin: 10px 0;
            padding: 8px;
            background: rgba(59, 130, 246, 0.1);
            border-radius: 4px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🛡️ Quick Icon Creator</h1>
        <p>Generate basic icons for the Fraud Detection Platform</p>
        
        <div class="icon-container">
            <h3>Generated Icons</h3>
            <canvas id="icon192" width="192" height="192"></canvas>
            <canvas id="icon512" width="512" height="512"></canvas>
            <br>
            <button onclick="downloadIcon(192)">Download logo192.png</button>
            <button onclick="downloadIcon(512)">Download logo512.png</button>
            <button onclick="generateFavicon()">Generate Favicon Data</button>
        </div>
        
        <div class="instructions">
            <h3>📋 Quick Setup Instructions</h3>
            <div class="step">
                <strong>Step 1:</strong> Click "Download logo192.png" and save it to the public folder
            </div>
            <div class="step">
                <strong>Step 2:</strong> Click "Download logo512.png" and save it to the public folder
            </div>
            <div class="step">
                <strong>Step 3:</strong> Click "Generate Favicon Data" and copy the data URI to use as a temporary favicon
            </div>
            <div class="step">
                <strong>Step 4:</strong> For a proper favicon.ico, use the generated PNGs with an online converter
            </div>
        </div>
        
        <div id="faviconData" style="display: none; margin-top: 20px; padding: 15px; background: #f3f4f6; border-radius: 6px; word-break: break-all; font-family: monospace; font-size: 12px;"></div>
    </div>

    <script>
        function drawIcon(canvas, size) {
            const ctx = canvas.getContext('2d');
            const scale = size / 64; // Base design is 64x64
            
            // Clear canvas
            ctx.clearRect(0, 0, size, size);
            
            // Background circle with gradient
            const gradient = ctx.createLinearGradient(0, 0, size, size);
            gradient.addColorStop(0, '#3b82f6');
            gradient.addColorStop(1, '#1d4ed8');
            
            ctx.fillStyle = gradient;
            ctx.beginPath();
            ctx.arc(size/2, size/2, size/2 - 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Border
            ctx.strokeStyle = '#1e40af';
            ctx.lineWidth = 2 * scale;
            ctx.stroke();
            
            // Shield shape
            ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
            ctx.strokeStyle = '#1e40af';
            ctx.lineWidth = 1.5 * scale;
            ctx.beginPath();
            ctx.moveTo(32*scale, 8*scale);
            ctx.lineTo(48*scale, 16*scale);
            ctx.lineTo(48*scale, 32*scale);
            ctx.quadraticCurveTo(48*scale, 48*scale, 32*scale, 56*scale);
            ctx.quadraticCurveTo(16*scale, 48*scale, 16*scale, 32*scale);
            ctx.lineTo(16*scale, 16*scale);
            ctx.closePath();
            ctx.fill();
            ctx.stroke();
            
            // Eye (ellipse)
            const eyeGradient = ctx.createLinearGradient(20*scale, 20*scale, 44*scale, 36*scale);
            eyeGradient.addColorStop(0, '#ef4444');
            eyeGradient.addColorStop(1, '#dc2626');
            
            ctx.fillStyle = eyeGradient;
            ctx.globalAlpha = 0.8;
            ctx.beginPath();
            ctx.ellipse(32*scale, 28*scale, 12*scale, 8*scale, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye highlight
            ctx.fillStyle = 'rgba(255, 255, 255, 0.3)';
            ctx.beginPath();
            ctx.ellipse(32*scale, 28*scale, 8*scale, 5*scale, 0, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye pupil
            ctx.globalAlpha = 1;
            ctx.fillStyle = '#1e293b';
            ctx.beginPath();
            ctx.arc(32*scale, 28*scale, 4*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Eye reflection
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(33*scale, 27*scale, 1.5*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Warning indicators
            ctx.globalAlpha = 0.8;
            ctx.fillStyle = '#f59e0b';
            
            ctx.beginPath();
            ctx.arc(24*scale, 40*scale, 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(32*scale, 42*scale, 1.5*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            ctx.beginPath();
            ctx.arc(40*scale, 40*scale, 2*scale, 0, 2 * Math.PI);
            ctx.fill();
            
            // Security lock
            ctx.globalAlpha = 0.6;
            ctx.fillStyle = '#10b981';
            ctx.fillRect(29*scale, 45*scale, 6*scale, 4*scale);
            
            // Lock shackle
            ctx.globalAlpha = 0.8;
            ctx.strokeStyle = '#10b981';
            ctx.lineWidth = 1.5 * scale;
            ctx.beginPath();
            ctx.arc(32*scale, 43*scale, 2*scale, Math.PI, 0, false);
            ctx.stroke();
            
            ctx.globalAlpha = 1;
        }
        
        function downloadIcon(size) {
            const canvas = document.getElementById(`icon${size}`);
            const link = document.createElement('a');
            link.download = `logo${size}.png`;
            link.href = canvas.toDataURL('image/png');
            link.click();
        }
        
        function generateFavicon() {
            // Create a small 32x32 canvas for favicon
            const canvas = document.createElement('canvas');
            canvas.width = 32;
            canvas.height = 32;
            drawIcon(canvas, 32);
            
            const dataUri = canvas.toDataURL('image/png');
            const faviconDiv = document.getElementById('faviconData');
            faviconDiv.innerHTML = `
                <h4>Favicon Data URI (copy this):</h4>
                <p style="background: white; padding: 10px; border: 1px solid #ddd; border-radius: 4px;">
                    ${dataUri}
                </p>
                <p><small>You can use this data URI directly in the HTML head tag as: 
                &lt;link rel="icon" href="${dataUri}" /&gt;</small></p>
            `;
            faviconDiv.style.display = 'block';
        }
        
        // Generate icons when page loads
        window.onload = function() {
            drawIcon(document.getElementById('icon192'), 192);
            drawIcon(document.getElementById('icon512'), 512);
        };
    </script>
</body>
</html>
