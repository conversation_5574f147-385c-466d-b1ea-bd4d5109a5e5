# Simplified Docker Compose configuration for fraud detection platform
version: '3.8'

services:
  # Consolidated Fraud API Service
  fraud-api:
    build:
      context: .
      dockerfile: Dockerfile.fraud-api
    container_name: fraud-api-service
    ports:
      - "8000:8000"
    networks:
      - fraud-network
    environment:
      - DATABASE_URL=sqlite:///./fraud.db
      - RISK_THRESHOLD=0.7
      - LOG_LEVEL=INFO
      - PYTHONPATH=/app
    volumes:
      - ./fraud-api:/app/fraud-api
      - ./fraud.db:/app/fraud.db
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/health"]
      interval: 15s
      timeout: 10s
      retries: 5
    restart: unless-stopped

  # Dashboard - React frontend
  dashboard:
    build:
      context: ./dashboard
      dockerfile: Dockerfile
    container_name: fraud-dashboard
    ports:
      - "3000:3000"
    networks:
      - fraud-network
    depends_on:
      fraud-api:
        condition: service_healthy
    environment:
      - REACT_APP_API_URL=http://localhost:8000
      - REACT_APP_WS_URL=ws://localhost:8000/ws
      - GENERATE_SOURCEMAP=false
      - SKIP_PREFLIGHT_CHECK=true
    volumes:
      - ./dashboard:/app
      - /app/node_modules
    restart: unless-stopped

networks:
  fraud-network:
    driver: bridge
    name: fraud-detection-network
