"""
Fraud Detection Platform - Consolidated API Service
This service combines ML fraud detection, transaction processing, and WebSocket updates.
"""

import os
import time
import logging
import asyncio
from datetime import datetime
from typing import Dict, Any, List, Set
from pathlib import Path

from fastapi import FastAP<PERSON>, HTTPException, WebSocket, WebSocketDisconnect, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.staticfiles import StaticFiles
from prometheus_client import Counter, Histogram, generate_latest
from sqlalchemy import create_engine, Column, Integer, String, Float, DateTime, Boolean, Text
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, Session
from pydantic import BaseModel
import pandas as pd
import numpy as np
from sklearn.ensemble import RandomForestClassifier
import joblib
import json

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Initialize FastAPI app
app = FastAPI(
    title="Fraud Detection API",
    description="Consolidated API for fraud detection, transaction processing, and case management",
    version="2.0.0"
)

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://localhost:3000", "http://127.0.0.1:3000"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Prometheus metrics
REQUESTS = Counter('fraud_api_requests_total', 'Total number of requests')
ERRORS = Counter('fraud_api_errors_total', 'Total number of errors')
PROCESSING_TIME = Histogram('fraud_api_processing_time_seconds', 'Time spent processing request')
TRANSACTIONS_PROCESSED = Counter('transactions_processed_total', 'Total transactions processed')
HIGH_RISK_ALERTS = Counter('high_risk_alerts_total', 'Total high risk alerts')

# Database setup
DATABASE_URL = os.getenv("DATABASE_URL", "sqlite:///./fraud.db")
engine = create_engine(DATABASE_URL, connect_args={"check_same_thread": False} if "sqlite" in DATABASE_URL else {})
SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
Base = declarative_base()

# Database Models
class Transaction(Base):
    __tablename__ = "transactions"
    
    id = Column(Integer, primary_key=True, index=True)
    transaction_id = Column(String, unique=True, index=True)
    step = Column(Integer)
    type = Column(String)
    amount = Column(Float)
    nameOrig = Column(String)
    oldbalanceOrg = Column(Float)
    newbalanceOrig = Column(Float)
    nameDest = Column(String)
    oldbalanceDest = Column(Float)
    newbalanceDest = Column(Float)
    risk_score = Column(Float)
    is_fraud = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)

class FraudCase(Base):
    __tablename__ = "fraud_cases"
    
    id = Column(Integer, primary_key=True, index=True)
    case_id = Column(String, unique=True, index=True)
    transaction_id = Column(String)
    status = Column(String, default="open")
    priority = Column(String, default="medium")
    assigned_to = Column(String)
    notes = Column(Text)
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow)

# Create tables
Base.metadata.create_all(bind=engine)

# Pydantic models
class TransactionRequest(BaseModel):
    transaction_id: str
    step: int
    type: str
    amount: float
    nameOrig: str
    oldbalanceOrg: float
    newbalanceOrig: float
    nameDest: str
    oldbalanceDest: float
    newbalanceDest: float

class TransactionResponse(BaseModel):
    transaction_id: str
    risk_score: float
    is_high_risk: bool
    timestamp: str

class HealthResponse(BaseModel):
    status: str
    version: str
    timestamp: datetime

# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Set[WebSocket] = set()

    async def connect(self, websocket: WebSocket):
        await websocket.accept()
        self.active_connections.add(websocket)
        logger.info(f"WebSocket connected. Total connections: {len(self.active_connections)}")

    def disconnect(self, websocket: WebSocket):
        self.active_connections.discard(websocket)
        logger.info(f"WebSocket disconnected. Total connections: {len(self.active_connections)}")

    async def broadcast(self, message: dict):
        if not self.active_connections:
            return
        
        disconnected = set()
        for connection in self.active_connections:
            try:
                await connection.send_json(message)
            except Exception:
                disconnected.add(connection)
        
        # Remove disconnected clients
        self.active_connections -= disconnected

manager = ConnectionManager()

# Simple ML Model (Enhanced with better features)
class SimpleFraudModel:
    def __init__(self):
        self.model = None
        self.feature_columns = [
            'step', 'amount', 'oldbalanceOrg', 'newbalanceOrig', 
            'oldbalanceDest', 'newbalanceDest', 'type_encoded'
        ]
        self.type_mapping = {'PAYMENT': 0, 'TRANSFER': 1, 'CASH_OUT': 2, 'DEBIT': 3, 'CASH_IN': 4}
        self._initialize_model()
    
    def _initialize_model(self):
        """Initialize a simple fraud detection model"""
        # Create a simple rule-based + ML hybrid model
        self.model = RandomForestClassifier(n_estimators=10, random_state=42)
        
        # Train with some synthetic data for demo purposes
        X_demo = np.random.rand(1000, len(self.feature_columns))
        y_demo = (X_demo[:, 1] > 0.8).astype(int)  # High amounts are more likely fraud
        
        self.model.fit(X_demo, y_demo)
        logger.info("Simple fraud model initialized")
    
    def predict_risk(self, transaction_data: dict) -> float:
        """Predict fraud risk for a transaction"""
        try:
            # Extract features
            features = [
                transaction_data.get('step', 0),
                transaction_data.get('amount', 0),
                transaction_data.get('oldbalanceOrg', 0),
                transaction_data.get('newbalanceOrig', 0),
                transaction_data.get('oldbalanceDest', 0),
                transaction_data.get('newbalanceDest', 0),
                self.type_mapping.get(transaction_data.get('type', 'PAYMENT'), 0)
            ]
            
            # Simple rule-based scoring
            risk_score = 0.0
            
            # High amount transactions
            if features[1] > 100000:  # amount > 100k
                risk_score += 0.3
            
            # Suspicious balance changes
            if features[2] > 0 and features[3] == 0:  # Balance went to zero
                risk_score += 0.4
            
            # Cash out transactions
            if transaction_data.get('type') == 'CASH_OUT':
                risk_score += 0.2
            
            # Add some ML prediction
            X = np.array(features).reshape(1, -1)
            ml_risk = self.model.predict_proba(X)[0][1] if len(self.model.classes_) > 1 else 0.1
            risk_score += ml_risk * 0.3
            
            return min(risk_score, 1.0)  # Cap at 1.0
            
        except Exception as e:
            logger.error(f"Error predicting risk: {e}")
            return 0.1  # Default low risk

# Initialize model
fraud_model = SimpleFraudModel()

# Database dependency
def get_db():
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# Risk threshold
RISK_THRESHOLD = float(os.getenv("RISK_THRESHOLD", "0.7"))

# API Endpoints
@app.get("/health", response_model=HealthResponse)
async def health_check():
    """Health check endpoint"""
    REQUESTS.inc()
    return {
        "status": "ok",
        "version": "2.0.0",
        "timestamp": datetime.now()
    }

@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint"""
    return generate_latest()

@app.post("/process", response_model=TransactionResponse)
async def process_transaction(
    transaction: TransactionRequest,
    db: Session = Depends(get_db)
):
    """Process a transaction and return risk score"""
    REQUESTS.inc()
    TRANSACTIONS_PROCESSED.inc()
    start_time = time.time()
    
    try:
        # Convert to dict for processing
        transaction_data = transaction.dict()
        
        # Predict fraud risk
        risk_score = fraud_model.predict_risk(transaction_data)
        is_high_risk = risk_score >= RISK_THRESHOLD
        
        if is_high_risk:
            HIGH_RISK_ALERTS.inc()
        
        # Save to database
        db_transaction = Transaction(
            transaction_id=transaction.transaction_id,
            step=transaction.step,
            type=transaction.type,
            amount=transaction.amount,
            nameOrig=transaction.nameOrig,
            oldbalanceOrg=transaction.oldbalanceOrg,
            newbalanceOrig=transaction.newbalanceOrig,
            nameDest=transaction.nameDest,
            oldbalanceDest=transaction.oldbalanceDest,
            newbalanceDest=transaction.newbalanceDest,
            risk_score=risk_score,
            is_fraud=is_high_risk
        )
        
        db.add(db_transaction)
        db.commit()
        
        # Broadcast to WebSocket clients
        await manager.broadcast({
            "type": "transaction",
            "data": {
                "transaction_id": transaction.transaction_id,
                "amount": transaction.amount,
                "type": transaction.type,
                "risk_score": risk_score,
                "is_high_risk": is_high_risk,
                "timestamp": datetime.now().isoformat()
            }
        })
        
        processing_time = time.time() - start_time
        PROCESSING_TIME.observe(processing_time)
        
        return TransactionResponse(
            transaction_id=transaction.transaction_id,
            risk_score=risk_score,
            is_high_risk=is_high_risk,
            timestamp=datetime.now().isoformat()
        )
        
    except Exception as e:
        ERRORS.inc()
        logger.error(f"Error processing transaction: {e}")
        raise HTTPException(status_code=500, detail=f"Processing error: {str(e)}")

@app.websocket("/ws")
async def websocket_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time updates"""
    await manager.connect(websocket)
    try:
        while True:
            # Keep connection alive
            await websocket.receive_text()
    except WebSocketDisconnect:
        manager.disconnect(websocket)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000)
