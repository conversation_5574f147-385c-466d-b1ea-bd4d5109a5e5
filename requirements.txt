# Fraud Detection Platform - Consolidated Dependencies
# This file contains all Python dependencies for the simplified platform

# Core FastAPI and web framework
fastapi==0.95.1
uvicorn==0.22.0
pydantic==1.10.7
python-multipart==0.0.6

# Machine Learning and Data Processing
pandas==2.0.1
numpy==1.24.3
scikit-learn==1.2.2
joblib==1.2.0
optuna>=3.0.0

# Database
sqlalchemy==2.0.15
sqlite3  # Built into Python

# Authentication and Security
PyJWT==2.8.0
passlib[bcrypt]==1.7.4
python-jose[cryptography]==3.3.0

# HTTP and WebSocket
requests==2.30.0
websockets==11.0.3

# Monitoring and Metrics
prometheus-client==0.16.0

# Development and Testing
pytest==7.3.1
pytest-asyncio==0.21.0
httpx==0.24.1  # For testing FastAPI

# Optional: PostgreSQL support for production
# psycopg2-binary==2.9.6  # Uncomment for PostgreSQL support
