import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Chevron pointing left
 * Category: ui
 */
export const ChevronLeftIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-chevronleft ${className}`}
      aria-label={ariaLabel || 'Chevron pointing left'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <polyline points="15,18 9,12 15,6" />
    </svg>
  );
};

export default ChevronLeftIcon;
