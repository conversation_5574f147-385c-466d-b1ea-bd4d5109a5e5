# Fraud Detection Platform

A streamlined full-stack fraud detection and investigation platform with real-time monitoring, case management, and analytics.

## 🏗️ Simplified Architecture

This platform provides a complete solution for detecting and investigating fraudulent transactions in real-time with a **simplified 2-service architecture**:

### Core Services
1. **Fraud API Service** (FastAPI) - Combined ML fraud detection + transaction processing + WebSocket updates
2. **Dashboard** (React/TypeScript) - Interactive frontend for monitoring and case management
3. **SQLite Database** - Local development database (PostgreSQL option for production)

### Simplified Architecture Diagram

```
┌─────────────────────────────────┐     ┌─────────────────┐
│                                 │     │                 │
│        Fraud API Service        │◀────│    Dashboard    │
│         (Port 8000)             │     │   (Port 3000)   │
│                                 │     │                 │
│  • ML Fraud Detection           │     │                 │
│  • Transaction Processing       │     │                 │
│  • WebSocket Real-time Updates  │     │                 │
│  • Case Management API          │     │                 │
│                                 │     │                 │
└────────────┬────────────────────┘     └─────────────────┘
             │
             ▼
    ┌─────────────────┐
    │                 │
    │ SQLite Database │
    │  (fraud.db)     │
    │                 │
    └─────────────────┘
```

## ✨ Features

- **Real-time Transaction Monitoring** - Live transaction processing with WebSocket updates
- **ML-based Fraud Detection** - Advanced machine learning models for risk scoring
- **Interactive Dashboard** - Modern React interface with risk heatmaps and analytics
- **Case Management System** - Complete fraud investigation workflow
- **Analytics & Reporting** - Comprehensive fraud analytics and insights
- **User Authentication** - Secure login and role-based access control
- **API Documentation** - Auto-generated OpenAPI/Swagger documentation
- **Database Integration** - Full PostgreSQL integration with audit trails
- **Containerized Deployment** - Docker and Docker Compose ready

## 🚀 Ultra-Simple Setup (Single Command!)

Get the entire fraud detection platform running with just **ONE** unified command:

```bash
# 1. Clone the repository
git clone <repository-url>
cd fraud-platform

# 2. Install all dependencies (Python + Node.js)
python setup.py

# 3. Start the entire platform (choose your mode)
python start.py                    # Local development (default)
python start.py --mode docker      # Docker containers
python start.py --stop             # Stop all services
```

**That's it!** The platform will be running at:
- **Dashboard**: http://localhost:3000
- **API**: http://localhost:8000
- **API Docs**: http://localhost:8000/docs

### Unified Startup Options

The new `start.py` script supports all deployment modes:

```bash
# Local development (SQLite + local services)
python start.py --mode local

# Docker deployment (containerized services)
python start.py --mode docker

# Stop all services (both local and Docker)
python start.py --stop

# Show version
python start.py --version
```

## 🔗 Access Points

After startup, access the platform at:

- **Dashboard**: http://localhost:3000
- **Fraud API Service**: http://localhost:8000
- **API Documentation**: http://localhost:8000/docs
- **Database**: fraud.db (SQLite file)

### Default Login Credentials
- **Username**: `analyst`
- **Password**: `password`

## 🧪 Testing

### Automated Platform Test

```bash
# Run comprehensive platform tests
python test_simplified_platform.py

# Run tests with wait (if services are starting)
python test_simplified_platform.py --wait
```

### Manual Testing

```bash
# Test API health
curl http://localhost:8000/health

# Test transaction processing
curl -X POST http://localhost:8000/process \
  -H "Content-Type: application/json" \
  -d '{"transaction_id": "test123", "step": 1, "type": "PAYMENT", "amount": 100, "nameOrig": "C123", "oldbalanceOrg": 1000, "newbalanceOrig": 900, "nameDest": "M456", "oldbalanceDest": 0, "newbalanceDest": 100}'

# View API documentation
open http://localhost:8000/docs
```

## 🛠️ Development

### Prerequisites

- **Python 3.11+** - For the fraud API service
- **Node.js 16+** - For React dashboard
- **Git** - Version control
- **Docker** (optional) - For containerized deployment

### Environment Configuration

The simplified platform uses minimal configuration:

```bash
# Database (SQLite for local development)
DATABASE_URL=sqlite:///./fraud.db

# API Configuration
RISK_THRESHOLD=0.7

# Dashboard
REACT_APP_API_URL=http://localhost:8000
REACT_APP_WS_URL=ws://localhost:8000/ws
```

### Service Details

#### Fraud API Service (Port 8000)
- **Framework**: FastAPI
- **Features**: ML fraud detection, transaction processing, WebSocket updates, case management
- **Key Endpoints**:
  - `GET /health` - Health check
  - `POST /process` - Process transactions and get risk scores
  - `WS /ws` - WebSocket for real-time updates
  - `GET /docs` - API documentation
  - `GET /metrics` - Prometheus metrics

#### Dashboard (Port 3000)
- **Framework**: React with TypeScript
- **Features**: Transaction monitoring, case management, analytics
- **Technologies**: React Router, Chart.js, TailwindCSS, Axios

### Database Schema

The platform uses SQLite (local) or PostgreSQL (production) with the following main tables:

- `transactions` - Transaction records with risk scores
- `fraud_cases` - Fraud investigation cases
- Built-in user authentication system

## 📊 Monitoring & Analytics

### Built-in Analytics
- Daily transaction summaries
- Risk score distributions
- Fraud detection rates
- Case management metrics

### Prometheus Metrics
Both services expose Prometheus metrics at `/metrics`:
- Request counts and latencies
- Error rates
- Processing times
- Custom business metrics

## 🔒 Security

- **Authentication**: Username/password with JWT tokens
- **Authorization**: Role-based access control
- **Database**: Parameterized queries to prevent SQL injection
- **API**: Input validation and sanitization
- **Audit Trail**: Complete audit logging for all actions

## 🚢 Deployment

### Docker Production Deployment

```bash
# Production deployment with Docker
docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d
```

### Kubernetes Deployment

```bash
# Deploy to Kubernetes (if Helm charts are available)
helm install fraud-platform ./infra/charts
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request

## 📝 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Troubleshooting

### Common Issues

1. **Port conflicts**: Ensure ports 3000 and 8000 are available
2. **Docker issues**: Restart Docker Desktop and try again
3. **Database connection**: SQLite database will be created automatically
4. **Node modules**: Delete `node_modules` and run `npm install` again
5. **Python dependencies**: Use a virtual environment and install requirements

### Getting Help

- Check the integration test results: `python integration_test.py`
- Review service logs: `docker-compose logs <service-name>`
- Verify service health: `curl http://localhost:8000/health`

### Performance Tuning

- Adjust Kafka retention settings for high-volume environments
- Configure PostgreSQL connection pooling
- Enable Redis caching for frequently accessed data
- Scale services horizontally using Docker Swarm or Kubernetes
