import React, { useState } from 'react';
import '../styles/CaseDrawer.css';
import { createCase } from '../services/api';
import { useAuth } from '../services/AuthContext';
import { Transaction, CaseTag, CaseStatus } from '../types';

interface CaseDrawerProps {
  isOpen: boolean;
  onClose: () => void;
  transaction: Transaction | null;
}

const CaseDrawer: React.FC<CaseDrawerProps> = ({ isOpen, onClose, transaction }) => {
  const [tag, setTag] = useState<CaseTag>(CaseTag.NEEDS_REVIEW);
  const [comment, setComment] = useState('');
  const [status, setStatus] = useState<CaseStatus>(CaseStatus.OPEN);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState('');
  const [success, setSuccess] = useState('');
  const { user } = useAuth();

  if (!isOpen || !transaction) {
    return null;
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
    setError('');
    setSuccess('');
    setIsSubmitting(true);

    try {
      await createCase({
        transaction_id: transaction.transaction_id || transaction.nameOrig,
        tag,
        comment,
        status
      });
      setSuccess('Case created successfully');
      setComment('');
      setTimeout(() => {
        onClose();
        setSuccess('');
      }, 2000);
    } catch (err) {
      setError('Failed to create case. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const formatCurrency = (amount: number): string => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD'
    }).format(amount);
  };

  const getRiskClass = (riskScore: number): string => {
    if (riskScore >= 0.8) return 'high-risk';
    if (riskScore >= 0.5) return 'medium-risk';
    return 'low-risk';
  };

  return (
    <div className={`case-drawer ${isOpen ? 'open' : ''}`}>
      <div className="drawer-header">
        <h2>Transaction Details</h2>
        <button className="close-button" onClick={onClose}>×</button>
      </div>

      <div className="drawer-content">
        <div className="transaction-details">
          <div className="detail-group">
            <h3>Basic Information</h3>
            <div className="detail-row">
              <span className="detail-label">Transaction ID:</span>
              <span className="detail-value">{transaction.transaction_id || transaction.nameOrig}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Type:</span>
              <span className="detail-value">{transaction.type}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Amount:</span>
              <span className="detail-value">{formatCurrency(transaction.amount)}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Risk Score:</span>
              <span className={`detail-value risk-badge ${getRiskClass(transaction.risk_score || 0)}`}>
                {((transaction.risk_score || 0) * 100).toFixed(1)}%
              </span>
            </div>
          </div>

          <div className="detail-group">
            <h3>Account Information</h3>
            <div className="detail-row">
              <span className="detail-label">Origin Account:</span>
              <span className="detail-value">{transaction.nameOrig}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Origin Old Balance:</span>
              <span className="detail-value">{formatCurrency(transaction.oldbalanceOrg)}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Origin New Balance:</span>
              <span className="detail-value">{formatCurrency(transaction.newbalanceOrig)}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Destination Account:</span>
              <span className="detail-value">{transaction.nameDest}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Destination Old Balance:</span>
              <span className="detail-value">{formatCurrency(transaction.oldbalanceDest)}</span>
            </div>
            <div className="detail-row">
              <span className="detail-label">Destination New Balance:</span>
              <span className="detail-value">{formatCurrency(transaction.newbalanceDest)}</span>
            </div>
          </div>

          {transaction.merchantFlag !== undefined && (
            <div className="detail-group">
              <h3>Additional Features</h3>
              <div className="detail-row">
                <span className="detail-label">Merchant Flag:</span>
                <span className="detail-value">{transaction.merchantFlag ? 'Yes' : 'No'}</span>
              </div>
              {transaction.balanceDiffOrig !== undefined && (
                <div className="detail-row">
                  <span className="detail-label">Origin Balance Diff:</span>
                  <span className="detail-value">{formatCurrency(transaction.balanceDiffOrig)}</span>
                </div>
              )}
              {transaction.balanceDiffDest !== undefined && (
                <div className="detail-row">
                  <span className="detail-label">Dest Balance Diff:</span>
                  <span className="detail-value">{formatCurrency(transaction.balanceDiffDest)}</span>
                </div>
              )}
            </div>
          )}
        </div>

        {user && (
          <div className="case-form">
            <h3>Create Case</h3>
            {error && <div className="error-message">{error}</div>}
            {success && <div className="success-message">{success}</div>}
            <form onSubmit={handleSubmit}>
              <div className="form-group">
                <label htmlFor="tag">Tag:</label>
                <select
                  id="tag"
                  value={tag}
                  onChange={(e) => setTag(e.target.value as CaseTag)}
                  disabled={isSubmitting}
                >
                  <option value="NEEDS_REVIEW">Needs Review</option>
                  <option value="SUSPICIOUS">Suspicious</option>
                  <option value="CONFIRMED">Confirmed Fraud</option>
                  <option value="FP">False Positive</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="status">Status:</label>
                <select
                  id="status"
                  value={status}
                  onChange={(e) => setStatus(e.target.value as CaseStatus)}
                  disabled={isSubmitting}
                >
                  <option value="open">Open</option>
                  <option value="pending">Pending</option>
                  <option value="closed">Closed</option>
                </select>
              </div>
              <div className="form-group">
                <label htmlFor="comment">Comment:</label>
                <textarea
                  id="comment"
                  value={comment}
                  onChange={(e) => setComment(e.target.value)}
                  disabled={isSubmitting}
                  rows={4}
                ></textarea>
              </div>
              <button
                type="submit"
                className="submit-button"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Creating...' : 'Create Case'}
              </button>
            </form>
          </div>
        )}
      </div>
    </div>
  );
};

export default CaseDrawer;
