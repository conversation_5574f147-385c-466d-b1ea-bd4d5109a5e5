/**
 * Icon Components for Fraud Detection Platform
 *
 * This file exports all generated icon components for easy importing.
 *
 * Usage:
 * import { DashboardIcon, AlertTriangleIcon } from '../components/icons';
 */

export { DashboardIcon } from './DashboardIcon';
export { AnalyticsIcon } from './AnalyticsIcon';
export { CasesIcon } from './CasesIcon';
export { UsersIcon } from './UsersIcon';
export { ReportsIcon } from './ReportsIcon';
export { SettingsIcon } from './SettingsIcon';
export { AlertTriangleIcon } from './AlertTriangleIcon';
export { CheckCircleIcon } from './CheckCircleIcon';
export { XCircleIcon } from './XCircleIcon';
export { InfoIcon } from './InfoIcon';
export { SearchIcon } from './SearchIcon';
export { FilterIcon } from './FilterIcon';
export { RefreshIcon } from './RefreshIcon';
export { EditIcon } from './EditIcon';
export { TrashIcon } from './TrashIcon';
export { PlusIcon } from './PlusIcon';
export { SaveIcon } from './SaveIcon';
export { MenuIcon } from './MenuIcon';
export { XIcon } from './XIcon';
export { ChevronDownIcon } from './ChevronDownIcon';
export { ChevronUpIcon } from './ChevronUpIcon';
export { ChevronLeftIcon } from './ChevronLeftIcon';
export { ChevronRightIcon } from './ChevronRightIcon';
export { EyeIcon } from './EyeIcon';
export { EyeOffIcon } from './EyeOffIcon';
export { ShieldIcon } from './ShieldIcon';
export { ShieldCheckIcon } from './ShieldCheckIcon';
export { LockIcon } from './LockIcon';
export { UnlockIcon } from './UnlockIcon';
export { BarChartIcon } from './BarChartIcon';
export { TrendingUpIcon } from './TrendingUpIcon';
export { TrendingDownIcon } from './TrendingDownIcon';
export { ActivityIcon } from './ActivityIcon';

// Re-export types for convenience
export type { IconProps, IconSize, IconColor } from '../../types/icons';
