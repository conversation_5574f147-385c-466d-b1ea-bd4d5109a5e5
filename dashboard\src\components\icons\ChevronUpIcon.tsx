import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Chevron pointing up
 * Category: ui
 */
export const ChevronUpIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-chevronup ${className}`}
      aria-label={ariaLabel || 'Chevron pointing up'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <polyline points="18,15 12,9 6,15" />
    </svg>
  );
};

export default ChevronUpIcon;
