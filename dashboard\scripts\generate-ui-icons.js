#!/usr/bin/env node

/**
 * UI Icon Generator Script for Fraud Detection Platform
 *
 * This script generates SVG-based React icon components without requiring external dependencies.
 * It creates consistent, customizable icons that match the dashboard's design system.
 *
 * Usage:
 * node scripts/generate-ui-icons.js
 */

const fs = require('fs');
const path = require('path');

/**
 * Icon definitions for the fraud detection platform
 */
const ICON_DEFINITIONS = [
  // Navigation Icons
  {
    name: 'Dashboard',
    category: 'navigation',
    description: 'Dashboard overview icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <rect x="3" y="3" width="7" height="7" rx="1" />
      <rect x="14" y="3" width="7" height="7" rx="1" />
      <rect x="14" y="14" width="7" height="7" rx="1" />
      <rect x="3" y="14" width="7" height="7" rx="1" />
    `
  },
  {
    name: 'Analytics',
    category: 'navigation',
    description: 'Analytics and charts icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M3 3v18h18" />
      <path d="M18.7 8l-5.1 5.2-2.8-2.7L7 14.3" />
    `
  },
  {
    name: 'Cases',
    category: 'navigation',
    description: 'Case management icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z" />
      <polyline points="14,2 14,8 20,8" />
      <line x1="16" y1="13" x2="8" y2="13" />
      <line x1="16" y1="17" x2="8" y2="17" />
      <polyline points="10,9 9,9 8,9" />
    `
  },
  {
    name: 'Users',
    category: 'navigation',
    description: 'Users and people icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
      <circle cx="12" cy="7" r="4" />
    `
  },
  {
    name: 'Reports',
    category: 'navigation',
    description: 'Reports and documents icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" />
      <polyline points="13,2 13,9 20,9" />
    `
  },
  {
    name: 'Settings',
    category: 'navigation',
    description: 'Settings and configuration icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <circle cx="12" cy="12" r="3" />
      <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z" />
    `
  },

  // Status Icons
  {
    name: 'AlertTriangle',
    category: 'status',
    description: 'Warning alert icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" />
      <line x1="12" y1="9" x2="12" y2="13" />
      <line x1="12" y1="17" x2="12.01" y2="17" />
    `
  },
  {
    name: 'CheckCircle',
    category: 'status',
    description: 'Success check icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14" />
      <polyline points="22,4 12,14.01 9,11.01" />
    `
  },
  {
    name: 'XCircle',
    category: 'status',
    description: 'Error or close icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <circle cx="12" cy="12" r="10" />
      <line x1="15" y1="9" x2="9" y2="15" />
      <line x1="9" y1="9" x2="15" y2="15" />
    `
  },
  {
    name: 'Info',
    category: 'status',
    description: 'Information icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <circle cx="12" cy="12" r="10" />
      <line x1="12" y1="16" x2="12" y2="12" />
      <line x1="12" y1="8" x2="12.01" y2="8" />
    `
  },

  // Action Icons
  {
    name: 'Search',
    category: 'actions',
    description: 'Search icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <circle cx="11" cy="11" r="8" />
      <path d="M21 21l-4.35-4.35" />
    `
  },
  {
    name: 'Filter',
    category: 'actions',
    description: 'Filter icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polygon points="22,3 2,3 10,12.46 10,19 14,21 14,12.46" />
    `
  },
  {
    name: 'Refresh',
    category: 'actions',
    description: 'Refresh or reload icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="23,4 23,10 17,10" />
      <polyline points="1,20 1,14 7,14" />
      <path d="M20.49 9A9 9 0 0 0 5.64 5.64L1 10m22 4l-4.64 4.36A9 9 0 0 1 3.51 15" />
    `
  },
  {
    name: 'Edit',
    category: 'actions',
    description: 'Edit or modify icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
      <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
    `
  },
  {
    name: 'Trash',
    category: 'actions',
    description: 'Delete or remove icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="3,6 5,6 21,6" />
      <path d="M19 6v14a2 2 0 0 1-2 2H7a2 2 0 0 1-2-2V6m3 0V4a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v2" />
      <line x1="10" y1="11" x2="10" y2="17" />
      <line x1="14" y1="11" x2="14" y2="17" />
    `
  },
  {
    name: 'Plus',
    category: 'actions',
    description: 'Add or create icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <line x1="12" y1="5" x2="12" y2="19" />
      <line x1="5" y1="12" x2="19" y2="12" />
    `
  },
  {
    name: 'Save',
    category: 'actions',
    description: 'Save icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z" />
      <polyline points="17,21 17,13 7,13 7,21" />
      <polyline points="7,3 7,8 15,8" />
    `
  },

  // UI Icons
  {
    name: 'Menu',
    category: 'ui',
    description: 'Menu or hamburger icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <line x1="3" y1="6" x2="21" y2="6" />
      <line x1="3" y1="12" x2="21" y2="12" />
      <line x1="3" y1="18" x2="21" y2="18" />
    `
  },
  {
    name: 'X',
    category: 'ui',
    description: 'Close or cancel icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <line x1="18" y1="6" x2="6" y2="18" />
      <line x1="6" y1="6" x2="18" y2="18" />
    `
  },
  {
    name: 'ChevronDown',
    category: 'ui',
    description: 'Chevron pointing down',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="6,9 12,15 18,9" />
    `
  },
  {
    name: 'ChevronUp',
    category: 'ui',
    description: 'Chevron pointing up',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="18,15 12,9 6,15" />
    `
  },
  {
    name: 'ChevronLeft',
    category: 'ui',
    description: 'Chevron pointing left',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="15,18 9,12 15,6" />
    `
  },
  {
    name: 'ChevronRight',
    category: 'ui',
    description: 'Chevron pointing right',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="9,18 15,12 9,6" />
    `
  },

  // Security Icons
  {
    name: 'Eye',
    category: 'security',
    description: 'View or monitor icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z" />
      <circle cx="12" cy="12" r="3" />
    `
  },
  {
    name: 'EyeOff',
    category: 'security',
    description: 'Hide or privacy icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
      <line x1="1" y1="1" x2="23" y2="23" />
    `
  },
  {
    name: 'Shield',
    category: 'security',
    description: 'Security and protection icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
    `
  },
  {
    name: 'ShieldCheck',
    category: 'security',
    description: 'Verified security icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z" />
      <path d="M9 12l2 2 4-4" />
    `
  },
  {
    name: 'Lock',
    category: 'security',
    description: 'Locked or secure icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
      <circle cx="12" cy="16" r="1" />
      <path d="M7 11V7a5 5 0 0 1 10 0v4" />
    `
  },
  {
    name: 'Unlock',
    category: 'security',
    description: 'Unlocked or accessible icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <rect x="3" y="11" width="18" height="11" rx="2" ry="2" />
      <circle cx="12" cy="16" r="1" />
      <path d="M7 11V7a5 5 0 0 1 9.9-1" />
    `
  },

  // Data Icons
  {
    name: 'BarChart',
    category: 'data',
    description: 'Bar chart icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <line x1="12" y1="20" x2="12" y2="10" />
      <line x1="18" y1="20" x2="18" y2="4" />
      <line x1="6" y1="20" x2="6" y2="16" />
    `
  },
  {
    name: 'TrendingUp',
    category: 'data',
    description: 'Trending up or growth icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" />
      <polyline points="17,6 23,6 23,12" />
    `
  },
  {
    name: 'TrendingDown',
    category: 'data',
    description: 'Trending down or decline icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="23,18 13.5,8.5 8.5,13.5 1,6" />
      <polyline points="17,18 23,18 23,12" />
    `
  },
  {
    name: 'Activity',
    category: 'data',
    description: 'Activity or pulse icon',
    viewBox: '0 0 24 24',
    type: 'stroke',
    content: `
      <polyline points="22,12 18,12 15,21 9,3 6,12 2,12" />
    `
  }
];

/**
 * Generate TypeScript React component for an icon
 */
function generateIconComponent(icon) {
  const componentName = `${icon.name}Icon`;

  return `import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * ${icon.description}
 * Category: ${icon.category}
 */
export const ${componentName}: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="${icon.viewBox}"
      fill={${icon.type === 'fill' ? 'iconColor' : '"none"'}}
      stroke={${icon.type === 'stroke' ? 'iconColor' : '"none"'}}
      strokeWidth={${icon.type === 'stroke' ? 'strokeWidth' : '0'}}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={\`icon icon-${icon.name.toLowerCase()} \${className}\`}
      aria-label={ariaLabel || '${icon.description}'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      ${icon.content.trim()}
    </svg>
  );
};

export default ${componentName};
`;
}

/**
 * Main function to generate all icon components
 */
async function generateIcons() {
  console.log('🎨 Generating UI icons for Fraud Detection Platform...\n');

  const iconsDir = path.join(__dirname, '..', 'src', 'components', 'icons');

  // Create icons directory if it doesn't exist
  if (!fs.existsSync(iconsDir)) {
    fs.mkdirSync(iconsDir, { recursive: true });
    console.log(`📁 Created directory: ${iconsDir}`);
  }

  // Generate individual icon components
  const exportStatements = [];

  for (const icon of ICON_DEFINITIONS) {
    const componentCode = generateIconComponent(icon);
    const fileName = `${icon.name}Icon.tsx`;
    const filePath = path.join(iconsDir, fileName);

    fs.writeFileSync(filePath, componentCode);
    console.log(`✅ Generated ${fileName}`);

    exportStatements.push(`export { ${icon.name}Icon } from './${icon.name}Icon';`);
  }

  // Generate index file for easy imports
  const indexContent = `/**
 * Icon Components for Fraud Detection Platform
 *
 * This file exports all generated icon components for easy importing.
 *
 * Usage:
 * import { DashboardIcon, AlertTriangleIcon } from '../components/icons';
 */

${exportStatements.join('\n')}

// Re-export types for convenience
export type { IconProps, IconSize, IconColor } from '../../types/icons';
`;

  const indexPath = path.join(iconsDir, 'index.ts');
  fs.writeFileSync(indexPath, indexContent);
  console.log(`✅ Generated index.ts`);

  console.log(`\n🎉 Successfully generated ${ICON_DEFINITIONS.length} icon components!`);
  console.log('\n📦 Generated files:');
  ICON_DEFINITIONS.forEach(icon => {
    console.log(`   - src/components/icons/${icon.name}Icon.tsx`);
  });
  console.log('   - src/components/icons/index.ts');

  console.log('\n💡 Usage example:');
  console.log(`   import { DashboardIcon, AlertTriangleIcon } from '../components/icons';`);
  console.log(`   <DashboardIcon size="lg" color="primary" />`);
  console.log(`   <AlertTriangleIcon size={24} color="#ef4444" />`);
}

// Run the generator if this script is executed directly
if (require.main === module) {
  generateIcons().catch(console.error);
}

module.exports = { generateIcons, ICON_DEFINITIONS };
