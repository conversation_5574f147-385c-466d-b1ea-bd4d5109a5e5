#!/usr/bin/env python3
"""
Test script for the simplified fraud detection platform.
This script tests the consolidated API service and verifies functionality.
"""

import requests
import json
import time
import sys
from datetime import datetime

class PlatformTester:
    def __init__(self):
        self.api_url = "http://localhost:8000"
        self.dashboard_url = "http://localhost:3000"
        
    def test_api_health(self):
        """Test API health endpoint"""
        print("🔍 Testing API health...")
        try:
            response = requests.get(f"{self.api_url}/health", timeout=5)
            if response.status_code == 200:
                data = response.json()
                print(f"✅ API is healthy: {data['status']} (v{data['version']})")
                return True
            else:
                print(f"❌ API health check failed: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ API health check failed: {e}")
            return False
    
    def test_transaction_processing(self):
        """Test transaction processing endpoint"""
        print("🔍 Testing transaction processing...")
        
        # Sample transaction data
        transaction_data = {
            "transaction_id": f"test_{int(time.time())}",
            "step": 1,
            "type": "TRANSFER",
            "amount": 50000.0,
            "nameOrig": "C123456789",
            "oldbalanceOrg": 60000.0,
            "newbalanceOrig": 10000.0,
            "nameDest": "C987654321",
            "oldbalanceDest": 5000.0,
            "newbalanceDest": 55000.0
        }
        
        try:
            response = requests.post(
                f"{self.api_url}/process",
                json=transaction_data,
                headers={"Content-Type": "application/json"},
                timeout=10
            )
            
            if response.status_code == 200:
                data = response.json()
                risk_score = data.get('risk_score', 0)
                is_high_risk = data.get('is_high_risk', False)
                print(f"✅ Transaction processed successfully:")
                print(f"   Risk Score: {risk_score:.3f}")
                print(f"   High Risk: {is_high_risk}")
                return True
            else:
                print(f"❌ Transaction processing failed: {response.status_code}")
                print(f"   Response: {response.text}")
                return False
                
        except requests.exceptions.RequestException as e:
            print(f"❌ Transaction processing failed: {e}")
            return False
    
    def test_multiple_transactions(self):
        """Test processing multiple transactions"""
        print("🔍 Testing multiple transaction processing...")
        
        transactions = [
            {
                "transaction_id": f"bulk_test_{i}_{int(time.time())}",
                "step": i,
                "type": "PAYMENT" if i % 2 == 0 else "TRANSFER",
                "amount": 1000.0 * (i + 1),
                "nameOrig": f"C{1000000 + i}",
                "oldbalanceOrg": 10000.0,
                "newbalanceOrig": 10000.0 - (1000.0 * (i + 1)),
                "nameDest": f"M{2000000 + i}",
                "oldbalanceDest": 0.0,
                "newbalanceDest": 1000.0 * (i + 1)
            }
            for i in range(5)
        ]
        
        success_count = 0
        for transaction in transactions:
            try:
                response = requests.post(
                    f"{self.api_url}/process",
                    json=transaction,
                    headers={"Content-Type": "application/json"},
                    timeout=5
                )
                
                if response.status_code == 200:
                    success_count += 1
                    data = response.json()
                    print(f"   ✅ Transaction {transaction['transaction_id']}: Risk {data.get('risk_score', 0):.3f}")
                else:
                    print(f"   ❌ Transaction {transaction['transaction_id']}: Failed ({response.status_code})")
                    
            except requests.exceptions.RequestException as e:
                print(f"   ❌ Transaction {transaction['transaction_id']}: Error ({e})")
        
        print(f"✅ Processed {success_count}/{len(transactions)} transactions successfully")
        return success_count == len(transactions)
    
    def test_dashboard_accessibility(self):
        """Test if dashboard is accessible"""
        print("🔍 Testing dashboard accessibility...")
        try:
            response = requests.get(self.dashboard_url, timeout=10)
            if response.status_code == 200:
                print("✅ Dashboard is accessible")
                return True
            else:
                print(f"❌ Dashboard not accessible: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ Dashboard not accessible: {e}")
            return False
    
    def test_api_documentation(self):
        """Test if API documentation is accessible"""
        print("🔍 Testing API documentation...")
        try:
            response = requests.get(f"{self.api_url}/docs", timeout=5)
            if response.status_code == 200:
                print("✅ API documentation is accessible")
                return True
            else:
                print(f"❌ API documentation not accessible: {response.status_code}")
                return False
        except requests.exceptions.RequestException as e:
            print(f"❌ API documentation not accessible: {e}")
            return False
    
    def run_all_tests(self):
        """Run all tests"""
        print("🚀 Starting Fraud Detection Platform Tests")
        print("=" * 50)
        
        tests = [
            ("API Health", self.test_api_health),
            ("Transaction Processing", self.test_transaction_processing),
            ("Multiple Transactions", self.test_multiple_transactions),
            ("Dashboard Accessibility", self.test_dashboard_accessibility),
            ("API Documentation", self.test_api_documentation),
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            print(f"\n📋 Running: {test_name}")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name}: PASSED")
                else:
                    print(f"❌ {test_name}: FAILED")
            except Exception as e:
                print(f"❌ {test_name}: ERROR - {e}")
        
        print("\n" + "=" * 50)
        print(f"🎯 Test Results: {passed}/{total} tests passed")
        
        if passed == total:
            print("🎉 All tests passed! Platform is working correctly.")
            print("\n📊 Access Points:")
            print(f"   Dashboard: {self.dashboard_url}")
            print(f"   API: {self.api_url}")
            print(f"   API Docs: {self.api_url}/docs")
            return True
        else:
            print("⚠️ Some tests failed. Please check the platform setup.")
            return False

def main():
    if len(sys.argv) > 1 and sys.argv[1] == "--wait":
        print("⏳ Waiting 10 seconds for services to start...")
        time.sleep(10)
    
    tester = PlatformTester()
    success = tester.run_all_tests()
    sys.exit(0 if success else 1)

if __name__ == "__main__":
    main()
