import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Trending up or growth icon
 * Category: data
 */
export const TrendingUpIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-trendingup ${className}`}
      aria-label={ariaLabel || 'Trending up or growth icon'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <polyline points="23,6 13.5,15.5 8.5,10.5 1,18" />
      <polyline points="17,6 23,6 23,12" />
    </svg>
  );
};

export default TrendingUpIcon;
