import React, { useState } from 'react';
import {
  DashboardIcon,
  AnalyticsIcon,
  CasesIcon,
  UsersIcon,
  ReportsIcon,
  SettingsIcon,
  AlertTriangleIcon,
  CheckCircleIcon,
  XCircleIcon,
  InfoIcon,
  SearchIcon,
  FilterIcon,
  RefreshIcon,
  EditIcon,
  TrashIcon,
  PlusIcon,
  SaveIcon,
  MenuIcon,
  XIcon,
  ChevronDownIcon,
  ChevronUpIcon,
  ChevronLeftIcon,
  ChevronRightIcon,
  EyeIcon,
  EyeOffIcon,
  ShieldIcon,
  ShieldCheckIcon,
  LockIcon,
  UnlockIcon,
  BarChartIcon,
  TrendingUpIcon,
  TrendingDownIcon,
  ActivityIcon,
  IconSize,
  IconColor
} from './icons';

/**
 * Demo component to showcase the generated icon system
 * This component demonstrates all available icons with different sizes and colors
 */
export const IconDemo: React.FC = () => {
  const [selectedSize, setSelectedSize] = useState<IconSize>('md');
  const [selectedColor, setSelectedColor] = useState<IconColor>('primary');

  const iconCategories = {
    navigation: [
      { component: DashboardIcon, name: 'Dashboard' },
      { component: AnalyticsIcon, name: 'Analy<PERSON>' },
      { component: CasesIcon, name: 'Cases' },
      { component: UsersIcon, name: 'Users' },
      { component: ReportsIcon, name: 'Reports' },
      { component: SettingsIcon, name: 'Settings' },
    ],
    status: [
      { component: AlertTriangleIcon, name: 'Alert Triangle' },
      { component: CheckCircleIcon, name: 'Check Circle' },
      { component: XCircleIcon, name: 'X Circle' },
      { component: InfoIcon, name: 'Info' },
    ],
    actions: [
      { component: SearchIcon, name: 'Search' },
      { component: FilterIcon, name: 'Filter' },
      { component: RefreshIcon, name: 'Refresh' },
      { component: EditIcon, name: 'Edit' },
      { component: TrashIcon, name: 'Trash' },
      { component: PlusIcon, name: 'Plus' },
      { component: SaveIcon, name: 'Save' },
    ],
    ui: [
      { component: MenuIcon, name: 'Menu' },
      { component: XIcon, name: 'X' },
      { component: ChevronDownIcon, name: 'Chevron Down' },
      { component: ChevronUpIcon, name: 'Chevron Up' },
      { component: ChevronLeftIcon, name: 'Chevron Left' },
      { component: ChevronRightIcon, name: 'Chevron Right' },
    ],
    security: [
      { component: EyeIcon, name: 'Eye' },
      { component: EyeOffIcon, name: 'Eye Off' },
      { component: ShieldIcon, name: 'Shield' },
      { component: ShieldCheckIcon, name: 'Shield Check' },
      { component: LockIcon, name: 'Lock' },
      { component: UnlockIcon, name: 'Unlock' },
    ],
    data: [
      { component: BarChartIcon, name: 'Bar Chart' },
      { component: TrendingUpIcon, name: 'Trending Up' },
      { component: TrendingDownIcon, name: 'Trending Down' },
      { component: ActivityIcon, name: 'Activity' },
    ],
  };

  const sizes: IconSize[] = ['xs', 'sm', 'md', 'lg', 'xl'];
  const colors: IconColor[] = ['primary', 'secondary', 'success', 'warning', 'danger', 'muted'];

  return (
    <div className="p-8 bg-gray-50 min-h-screen">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold text-gray-900 mb-8">
          🎨 Icon System Demo - Fraud Detection Platform
        </h1>

        {/* Controls */}
        <div className="bg-white rounded-lg shadow-sm p-6 mb-8">
          <h2 className="text-xl font-semibold mb-4">Controls</h2>
          <div className="flex flex-wrap gap-6">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Size</label>
              <select
                value={selectedSize}
                onChange={(e) => setSelectedSize(e.target.value as IconSize)}
                className="border border-gray-300 rounded-md px-3 py-2"
              >
                {sizes.map(size => (
                  <option key={size} value={size}>{size}</option>
                ))}
              </select>
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">Color</label>
              <select
                value={selectedColor}
                onChange={(e) => setSelectedColor(e.target.value as IconColor)}
                className="border border-gray-300 rounded-md px-3 py-2"
              >
                {colors.map(color => (
                  <option key={color} value={color}>{color}</option>
                ))}
              </select>
            </div>
          </div>
        </div>

        {/* Icon Categories */}
        {Object.entries(iconCategories).map(([category, icons]) => (
          <div key={category} className="bg-white rounded-lg shadow-sm p-6 mb-6">
            <h2 className="text-xl font-semibold mb-4 capitalize">
              {category} Icons
            </h2>
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-6">
              {icons.map(({ component: IconComponent, name }) => (
                <div
                  key={name}
                  className="flex flex-col items-center p-4 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  <IconComponent
                    size={selectedSize}
                    color={selectedColor}
                    className="mb-2"
                  />
                  <span className="text-xs text-gray-600 text-center">{name}</span>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* Usage Examples */}
        <div className="bg-white rounded-lg shadow-sm p-6">
          <h2 className="text-xl font-semibold mb-4">Usage Examples</h2>
          <div className="space-y-4">
            <div className="bg-gray-100 p-4 rounded-md">
              <h3 className="font-medium mb-2">Basic Usage</h3>
              <code className="text-sm">
                {`import { DashboardIcon } from '../components/icons';
<DashboardIcon size="lg" color="primary" />`}
              </code>
            </div>
            <div className="bg-gray-100 p-4 rounded-md">
              <h3 className="font-medium mb-2">With Custom Size and Color</h3>
              <code className="text-sm">
                {`<AlertTriangleIcon size={24} color="#ef4444" className="mr-2" />`}
              </code>
            </div>
            <div className="bg-gray-100 p-4 rounded-md">
              <h3 className="font-medium mb-2">Interactive Icon</h3>
              <code className="text-sm">
                {`<SearchIcon 
  size="md" 
  color="muted" 
  onClick={() => setSearchOpen(true)}
  className="cursor-pointer hover:text-blue-600"
/>`}
              </code>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default IconDemo;
