/**
 * Core type definitions for the Fraud Detection Platform
 */

// User and Authentication Types
export interface User {
  id: number;
  username: string;
  role: UserRole;
  created_at: string;
}

export enum UserRole {
  ANALYST = 'analyst',
  AUDITOR = 'auditor',
  ADMIN = 'admin'
}

export interface AuthContextType {
  user: User | null;
  login: (username: string, password: string) => Promise<boolean>;
  logout: () => void;
  isAuthenticated: boolean;
}

// Transaction Types
export interface Transaction {
  transaction_id: string;
  step: number;
  type: TransactionType;
  amount: number;
  nameOrig: string;
  oldbalanceOrg: number;
  newbalanceOrig: number;
  nameDest: string;
  oldbalanceDest: number;
  newbalanceDest: number;
  risk_score?: number;
  timestamp?: string;
  isFraud?: boolean;
  processed_at?: string;
  // Additional enriched features
  merchantFlag?: number;
  balanceDiffOrig?: number;
  balanceDiffDest?: number;
}

export enum TransactionType {
  PAYMENT = 'PAYMENT',
  TRANSFER = 'TRANSFER',
  CASH_OUT = 'CASH_OUT',
  DEBIT = 'DEBIT',
  CASH_IN = 'CASH_IN'
}

export interface TransactionRequest {
  transactions: Transaction[];
}

export interface RiskScore {
  transaction_id: string;
  risk: number;
}

export interface RiskScoreResponse {
  results: RiskScore[];
}

// Case Management Types
export interface Case {
  id: number;
  transaction_id: string;
  user_id: number;
  tag: CaseTag;
  comment?: string;
  status: CaseStatus;
  created_at: string;
  updated_at: string;
  user?: User;
  transaction?: Transaction;
}

export enum CaseStatus {
  OPEN = 'open',
  CLOSED = 'closed',
  PENDING = 'pending'
}

export enum CaseTag {
  FALSE_POSITIVE = 'FP',
  CONFIRMED = 'CONFIRMED',
  SUSPICIOUS = 'SUSPICIOUS',
  NEEDS_REVIEW = 'NEEDS_REVIEW'
}

export interface CaseCreate {
  transaction_id: string;
  tag: CaseTag;
  comment?: string;
  status?: CaseStatus;
}

export interface CaseUpdate {
  tag?: CaseTag;
  comment?: string;
  status?: CaseStatus;
}

export interface CaseFilter {
  status?: string;
  transaction_id?: string;
  tag?: CaseTag;
  user_id?: number;
  date_from?: string;
  date_to?: string;
}

// Analytics Types
export interface CaseStats {
  total_cases: number;
  open_cases: number;
  closed_cases: number;
  pending_cases: number;
  false_positives: number;
  confirmed_fraud: number;
  by_tag: Record<CaseTag, number>;
  by_status: Record<CaseStatus, number>;
}

export interface FeatureImportance {
  feature: string;
  importance: number;
}

export interface FraudTrendData {
  date: string;
  count: number;
}

export interface ModelInfo {
  name: string;
  version: string;
  accuracy: number;
  precision: number;
  recall: number;
  f1_score: number;
  last_trained: string;
}

// API Response Types
export interface ApiResponse<T> {
  data: T;
  message?: string;
  status: number;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  size: number;
  pages: number;
}

// WebSocket Types
export interface WebSocketMessage {
  type: 'transaction' | 'alert' | 'status';
  data: any;
  timestamp: string;
}

export interface TransactionWebSocketMessage extends WebSocketMessage {
  type: 'transaction';
  data: {
    transaction: Transaction;
    risk_score: number;
    timestamp: string;
  };
}

// Component Props Types
export interface TableColumn<T> {
  key: keyof T;
  label: string;
  sortable?: boolean;
  render?: (value: any, item: T) => React.ReactNode;
}

export interface SortConfig {
  key: string;
  direction: 'asc' | 'desc';
}

// Form Types
export interface FormField {
  name: string;
  label: string;
  type: 'text' | 'email' | 'password' | 'select' | 'textarea' | 'number';
  required?: boolean;
  options?: { value: string; label: string }[];
  validation?: (value: any) => string | undefined;
}

// Error Types
export interface ValidationError {
  field: string;
  message: string;
}

export interface FormErrors {
  [key: string]: string;
}

// Loading States
export interface LoadingState {
  isLoading: boolean;
  error?: string | null;
}

// Chart Types
export interface ChartData {
  labels: string[];
  datasets: {
    label: string;
    data: number[];
    backgroundColor?: string | string[];
    borderColor?: string | string[];
    borderWidth?: number;
  }[];
}

// Health Check Types
export interface HealthStatus {
  status: 'ok' | 'error';
  services?: {
    model: boolean;
    ingest: boolean;
    database?: boolean;
  };
  timestamp: string;
  error?: string;
}

// Statistics Types
export interface ServiceStats {
  total_processed: number;
  total_model_requests: number;
  total_model_errors: number;
  total_alerts: number;
  success_rate: number;
  avg_processing_time: number;
  uptime: number;
}

// Navigation Types
export interface NavItem {
  path: string;
  label: string;
  icon?: string;
  roles?: UserRole[];
}

// Theme Types
export interface Theme {
  colors: {
    primary: string;
    secondary: string;
    success: string;
    warning: string;
    danger: string;
    info: string;
    light: string;
    dark: string;
  };
  spacing: {
    xs: string;
    sm: string;
    md: string;
    lg: string;
    xl: string;
  };
}

// Utility Types
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;
export type RequiredFields<T, K extends keyof T> = T & Required<Pick<T, K>>;
export type DeepPartial<T> = {
  [P in keyof T]?: T[P] extends object ? DeepPartial<T[P]> : T[P];
};
