import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Reports and documents icon
 * Category: navigation
 */
export const ReportsIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-reports ${className}`}
      aria-label={ariaLabel || 'Reports and documents icon'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <path d="M13 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V9z" />
      <polyline points="13,2 13,9 20,9" />
    </svg>
  );
};

export default ReportsIcon;
