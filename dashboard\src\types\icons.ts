/**
 * Icon Types for Fraud Detection Platform
 * 
 * This file contains TypeScript types and interfaces for the icon system.
 */

export interface IconProps {
  /** Size of the icon in pixels or CSS units */
  size?: number | string;
  /** Color of the icon (CSS color value) */
  color?: string;
  /** Stroke width for outline icons */
  strokeWidth?: number;
  /** Additional CSS class names */
  className?: string;
  /** Accessibility label */
  'aria-label'?: string;
  /** Icon title for tooltips */
  title?: string;
  /** Click handler */
  onClick?: () => void;
  /** Additional props to pass to the SVG element */
  [key: string]: any;
}

export interface IconConfig {
  /** Icon name/identifier */
  name: string;
  /** SVG path data or element content */
  content: string;
  /** Default viewBox for the icon */
  viewBox?: string;
  /** Whether the icon uses fill or stroke */
  type: 'fill' | 'stroke';
  /** Icon category for organization */
  category: IconCategory;
  /** Description of the icon */
  description?: string;
}

export type IconCategory = 
  | 'navigation'
  | 'actions'
  | 'status'
  | 'data'
  | 'security'
  | 'ui';

export type IconSize = 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;

export type IconColor = 
  | 'primary'
  | 'secondary' 
  | 'success'
  | 'warning'
  | 'danger'
  | 'muted'
  | string;

/**
 * Predefined icon sizes in pixels
 */
export const ICON_SIZES: Record<Exclude<IconSize, number>, number> = {
  xs: 12,
  sm: 16,
  md: 20,
  lg: 24,
  xl: 32,
};

/**
 * Color mappings to CSS custom properties
 */
export const ICON_COLORS: Record<IconColor, string> = {
  primary: 'var(--primary-color)',
  secondary: 'var(--secondary-color)',
  success: 'var(--success-color)',
  warning: 'var(--warning-color)',
  danger: 'var(--danger-color)',
  muted: 'var(--text-light)',
};
