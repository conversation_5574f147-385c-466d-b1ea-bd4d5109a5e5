#!/usr/bin/env node

/**
 * Icon Generator Script for Fraud Detection Platform
 *
 * This script generates the required favicon and PWA icons from the SVG source.
 *
 * Prerequisites:
 * - Node.js installed
 * - sharp package: npm install sharp
 *
 * Usage:
 * node generate-icons.js
 */

const fs = require('fs');
const path = require('path');

// Check if sharp is available, if not create basic PNG icons using Canvas API
let sharp;
let useSharp = false;
try {
    sharp = require('sharp');
    useSharp = true;
    console.log('✅ Sharp package found. Using Sharp for icon generation.');
} catch (error) {
    console.log('⚠️  Sharp package not found. Creating basic PNG icons using Canvas API.');
}

// SVG content for the fraud detection icon
const svgContent = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 64 64" width="64" height="64">
  <defs>
    <linearGradient id="shieldGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#1d4ed8;stop-opacity:1" />
    </linearGradient>
    <linearGradient id="eyeGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#ef4444;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#dc2626;stop-opacity:1" />
    </linearGradient>
  </defs>

  <!-- Background circle -->
  <circle cx="32" cy="32" r="30" fill="url(#shieldGradient)" stroke="#1e40af" stroke-width="2"/>

  <!-- Shield shape -->
  <path d="M32 8 L48 16 L48 32 Q48 48 32 56 Q16 48 16 32 L16 16 Z"
        fill="rgba(255,255,255,0.9)"
        stroke="#1e40af"
        stroke-width="1.5"/>

  <!-- Eye symbol (fraud detection/monitoring) -->
  <ellipse cx="32" cy="28" rx="12" ry="8" fill="url(#eyeGradient)" opacity="0.8"/>
  <ellipse cx="32" cy="28" rx="8" ry="5" fill="rgba(255,255,255,0.3)"/>
  <circle cx="32" cy="28" r="4" fill="#1e293b"/>
  <circle cx="33" cy="27" r="1.5" fill="white"/>

  <!-- Warning/Alert indicators -->
  <circle cx="24" cy="40" r="2" fill="#f59e0b" opacity="0.8"/>
  <circle cx="32" cy="42" r="1.5" fill="#f59e0b" opacity="0.8"/>
  <circle cx="40" cy="40" r="2" fill="#f59e0b" opacity="0.8"/>

  <!-- Security lock accent -->
  <rect x="29" y="45" width="6" height="4" rx="1" fill="rgba(16,185,129,0.6)"/>
  <path d="M30 45 L30 43 Q30 41 32 41 Q34 41 34 43 L34 45"
        fill="none"
        stroke="rgba(16,185,129,0.8)"
        stroke-width="1.5"/>
</svg>`;

// Icon sizes to generate
const iconSizes = [
    { size: 16, name: 'favicon-16x16.png' },
    { size: 32, name: 'favicon-32x32.png' },
    { size: 64, name: 'favicon-64x64.png' },
    { size: 192, name: 'logo192.png' },
    { size: 512, name: 'logo512.png' }
];

async function generateIcons() {
    console.log('🛡️  Generating icons for Fraud Detection Platform...\n');

    const publicDir = path.join(__dirname, 'public');

    // Ensure public directory exists
    if (!fs.existsSync(publicDir)) {
        fs.mkdirSync(publicDir, { recursive: true });
    }

    if (useSharp) {
        try {
            // Generate PNG icons using Sharp
            for (const { size, name } of iconSizes) {
                const outputPath = path.join(publicDir, name);

                await sharp(Buffer.from(svgContent))
                    .resize(size, size)
                    .png()
                    .toFile(outputPath);

                console.log(`✅ Generated ${name} (${size}x${size})`);
            }

            // Generate favicon.ico (multi-size ICO file)
            console.log('\n📦 Generating favicon.ico...');

            // For now, just save the 32x32 as favicon.ico (most browsers will accept PNG)
            // For a true ICO file, you'd need a specialized library like 'to-ico'
            await sharp(Buffer.from(svgContent))
                .resize(32, 32)
                .png()
                .toFile(path.join(publicDir, 'favicon.ico'));

            console.log('✅ Generated favicon.ico (32x32 PNG format)');

            console.log('\n🎉 All icons generated successfully!');
            console.log('\n📁 Generated files:');
            iconSizes.forEach(({ name }) => {
                console.log(`   - public/${name}`);
            });
            console.log('   - public/favicon.ico');

            console.log('\n💡 Note: The favicon.ico is in PNG format. For better browser compatibility,');
            console.log('   consider converting it to true ICO format using an online converter.');

        } catch (error) {
            console.error('❌ Error generating icons:', error.message);
            process.exit(1);
        }
    } else {
        // Create basic PNG icons using Node.js Canvas API fallback
        try {
            const { createCanvas } = require('canvas');
            console.log('✅ Canvas package found. Using Canvas for icon generation.');

            for (const { size, name } of iconSizes) {
                const canvas = createCanvas(size, size);
                const ctx = canvas.getContext('2d');

                // Create a simple fraud detection icon
                // Background circle
                ctx.fillStyle = '#3b82f6';
                ctx.beginPath();
                ctx.arc(size/2, size/2, size/2 - 2, 0, 2 * Math.PI);
                ctx.fill();

                // Shield shape
                ctx.fillStyle = 'white';
                ctx.globalAlpha = 0.9;
                ctx.beginPath();
                const centerX = size/2;
                const centerY = size/2;
                const shieldWidth = size * 0.6;
                const shieldHeight = size * 0.7;

                ctx.moveTo(centerX, centerY - shieldHeight/2);
                ctx.lineTo(centerX + shieldWidth/2, centerY - shieldHeight/4);
                ctx.lineTo(centerX + shieldWidth/2, centerY + shieldHeight/4);
                ctx.lineTo(centerX, centerY + shieldHeight/2);
                ctx.lineTo(centerX - shieldWidth/2, centerY + shieldHeight/4);
                ctx.lineTo(centerX - shieldWidth/2, centerY - shieldHeight/4);
                ctx.closePath();
                ctx.fill();

                // Eye symbol
                ctx.globalAlpha = 0.8;
                ctx.fillStyle = '#ef4444';
                ctx.beginPath();
                ctx.ellipse(centerX, centerY - size*0.1, size*0.2, size*0.12, 0, 0, 2 * Math.PI);
                ctx.fill();

                // Eye center
                ctx.fillStyle = '#1e293b';
                ctx.beginPath();
                ctx.arc(centerX, centerY - size*0.1, size*0.08, 0, 2 * Math.PI);
                ctx.fill();

                // Eye highlight
                ctx.fillStyle = 'white';
                ctx.beginPath();
                ctx.arc(centerX + size*0.02, centerY - size*0.12, size*0.03, 0, 2 * Math.PI);
                ctx.fill();

                // Warning dots
                ctx.fillStyle = '#f59e0b';
                ctx.beginPath();
                ctx.arc(centerX - size*0.15, centerY + size*0.15, size*0.04, 0, 2 * Math.PI);
                ctx.fill();

                ctx.beginPath();
                ctx.arc(centerX + size*0.15, centerY + size*0.15, size*0.04, 0, 2 * Math.PI);
                ctx.fill();

                // Security lock
                ctx.fillStyle = '#10b981';
                ctx.fillRect(centerX - size*0.06, centerY + size*0.2, size*0.12, size*0.08);

                const buffer = canvas.toBuffer('image/png');
                const outputPath = path.join(publicDir, name);
                fs.writeFileSync(outputPath, buffer);

                console.log(`✅ Generated ${name} (${size}x${size})`);
            }

            // Generate favicon.ico using the 32x32 version
            const canvas32 = createCanvas(32, 32);
            const ctx32 = canvas32.getContext('2d');

            // Same icon drawing code but for 32x32
            ctx32.fillStyle = '#3b82f6';
            ctx32.beginPath();
            ctx32.arc(16, 16, 14, 0, 2 * Math.PI);
            ctx32.fill();

            ctx32.fillStyle = 'white';
            ctx32.globalAlpha = 0.9;
            ctx32.beginPath();
            ctx32.moveTo(16, 6);
            ctx32.lineTo(26, 10);
            ctx32.lineTo(26, 20);
            ctx32.lineTo(16, 26);
            ctx32.lineTo(6, 20);
            ctx32.lineTo(6, 10);
            ctx32.closePath();
            ctx32.fill();

            const faviconBuffer = canvas32.toBuffer('image/png');
            fs.writeFileSync(path.join(publicDir, 'favicon.ico'), faviconBuffer);

            console.log('✅ Generated favicon.ico (32x32 PNG format)');
            console.log('\n🎉 All icons generated successfully using Canvas!');

        } catch (canvasError) {
            console.log('⚠️  Canvas package not found either. Providing manual instructions.');
            console.log('   To use automatic generation, install either:');
            console.log('   npm install sharp  (recommended)');
            console.log('   or');
            console.log('   npm install canvas');
        }
        // Provide manual instructions
        console.log('📋 Manual Icon Generation Instructions:\n');

        // Save the SVG file for manual conversion
        const svgPath = path.join(publicDir, 'fraud-icon.svg');
        fs.writeFileSync(svgPath, svgContent);
        console.log(`✅ Created SVG source file: ${svgPath}`);

        console.log('\n🔧 Option 1: Use the HTML generator');
        console.log(`   1. Open: file://${path.join(publicDir, 'create-basic-icons.html')}`);
        console.log('   2. Click "Download PNG" buttons for each size');
        console.log('   3. Save files as logo192.png and logo512.png in public folder');

        console.log('\n🌐 Option 2: Use online converters');
        console.log('   1. Upload fraud-icon.svg to:');
        console.log('      - https://www.favicon-generator.org/');
        console.log('      - https://realfavicongenerator.net/');
        console.log('      - https://convertio.co/svg-png/');
        console.log('   2. Generate the required sizes: 192x192, 512x512, and favicon.ico');
        console.log('   3. Download and place in public folder');

        console.log('\n⚡ Option 3: Install Sharp for automatic generation');
        console.log('   npm install sharp && node generate-icons.js');

        console.log('\n📱 Required files:');
        iconSizes.forEach(({ name, size }) => {
            console.log(`   - ${name} (${size}x${size})`);
        });
        console.log('   - favicon.ico (16x16, 32x32, 64x64 combined)');

        console.log('\n🎯 Temporary Solution:');
        console.log('   The index.html already includes a data URI favicon that will work');
        console.log('   immediately. Generate the PNG files when convenient.');
    }

    // Clean up temporary files
    const tempFiles = ['generate-icons.html'];
    tempFiles.forEach(file => {
        const filePath = path.join(publicDir, file);
        if (fs.existsSync(filePath)) {
            fs.unlinkSync(filePath);
            console.log(`🧹 Cleaned up ${file}`);
        }
    });
}

// Check if this script is being run directly
if (require.main === module) {
    generateIcons();
}

module.exports = { generateIcons, svgContent };
