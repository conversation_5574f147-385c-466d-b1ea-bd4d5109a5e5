import React from 'react';
import { IconProps, ICON_SIZES, ICON_COLORS } from '../../types/icons';

/**
 * Hide or privacy icon
 * Category: security
 */
export const EyeOffIcon: React.FC<IconProps> = ({
  size = 'md',
  color = 'currentColor',
  strokeWidth = 2,
  className = '',
  'aria-label': ariaLabel,
  title,
  ...props
}) => {
  const iconSize = typeof size === 'number' ? size : ICON_SIZES[size];
  const iconColor = ICON_COLORS[color] || color;

  return (
    <svg
      width={iconSize}
      height={iconSize}
      viewBox="0 0 24 24"
      fill={"none"}
      stroke={iconColor}
      strokeWidth={strokeWidth}
      strokeLinecap="round"
      strokeLinejoin="round"
      className={`icon icon-eyeoff ${className}`}
      aria-label={ariaLabel || 'Hide or privacy icon'}
      role="img"
      {...(title && { title })}
      {...props}
    >
      <path d="M17.94 17.94A10.07 10.07 0 0 1 12 20c-7 0-11-8-11-8a18.45 18.45 0 0 1 5.06-5.94M9.9 4.24A9.12 9.12 0 0 1 12 4c7 0 11 8 11 8a18.5 18.5 0 0 1-2.16 3.19m-6.72-1.07a3 3 0 1 1-4.24-4.24" />
      <line x1="1" y1="1" x2="23" y2="23" />
    </svg>
  );
};

export default EyeOffIcon;
