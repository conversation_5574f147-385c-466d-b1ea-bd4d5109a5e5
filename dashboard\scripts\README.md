# 🎨 Icon Generation System - Fraud Detection Platform

This directory contains scripts for generating a comprehensive set of UI icons for the fraud detection platform dashboard. The system creates SVG-based React components without requiring external icon libraries or dependencies.

## 🚀 Quick Start

### Generate Icons

```bash
# Navigate to dashboard directory
cd dashboard

# Run the icon generator
node scripts/generate-ui-icons.js
```

This will create:
- Individual icon components in `src/components/icons/`
- TypeScript types in `src/types/icons.ts`
- Index file for easy imports

### Use Icons in Components

```tsx
import { DashboardIcon, AlertTriangleIcon, SearchIcon } from '../components/icons';

function MyComponent() {
  return (
    <div>
      <DashboardIcon size="lg" color="primary" />
      <AlertTriangleIcon size={24} color="#ef4444" />
      <SearchIcon size="md" color="muted" className="mr-2" />
    </div>
  );
}
```

## 📦 Generated Icons

### Navigation Icons
- `DashboardIcon` - Dashboard overview
- `AnalyticsIcon` - Analytics and charts
- `CasesIcon` - Case management
- `UsersIcon` - Users and people
- `ReportsIcon` - Reports and documents
- `SettingsIcon` - Settings and configuration

### Status Icons
- `AlertTriangleIcon` - Warning alerts
- `CheckCircleIcon` - Success states
- `XCircleIcon` - Error states
- `InfoIcon` - Information

### Action Icons
- `SearchIcon` - Search functionality
- `FilterIcon` - Filter data
- `RefreshIcon` - Refresh/reload
- `EditIcon` - Edit/modify
- `TrashIcon` - Delete/remove
- `PlusIcon` - Add/create
- `SaveIcon` - Save data

### UI Icons
- `MenuIcon` - Menu/hamburger
- `XIcon` - Close/cancel
- `ChevronDownIcon` - Dropdown indicator
- `ChevronUpIcon` - Collapse indicator
- `ChevronLeftIcon` - Previous/back
- `ChevronRightIcon` - Next/forward

### Security Icons
- `EyeIcon` - View/monitor
- `EyeOffIcon` - Hide/privacy
- `ShieldIcon` - Security/protection
- `ShieldCheckIcon` - Verified security
- `LockIcon` - Locked/secure
- `UnlockIcon` - Unlocked/accessible

### Data Icons
- `BarChartIcon` - Bar charts
- `TrendingUpIcon` - Growth/increase
- `TrendingDownIcon` - Decline/decrease
- `ActivityIcon` - Activity/pulse

## 🎛️ Icon Properties

All icons support the following props:

```tsx
interface IconProps {
  size?: 'xs' | 'sm' | 'md' | 'lg' | 'xl' | number;
  color?: 'primary' | 'secondary' | 'success' | 'warning' | 'danger' | 'muted' | string;
  strokeWidth?: number;
  className?: string;
  'aria-label'?: string;
  title?: string;
  onClick?: () => void;
}
```

### Size Options
- `xs`: 12px
- `sm`: 16px
- `md`: 20px (default)
- `lg`: 24px
- `xl`: 32px
- Custom: Any number for pixel size

### Color Options
- `primary`: Blue (#3b82f6)
- `secondary`: Gray (#64748b)
- `success`: Green (#10b981)
- `warning`: Amber (#f59e0b)
- `danger`: Red (#ef4444)
- `muted`: Light gray (#64748b)
- Custom: Any CSS color value

## 🛠️ Customization

### Adding New Icons

1. Edit `scripts/generate-ui-icons.js`
2. Add new icon definition to `ICON_DEFINITIONS` array:

```javascript
{
  name: 'MyNewIcon',
  category: 'actions',
  description: 'My new icon description',
  viewBox: '0 0 24 24',
  type: 'stroke',
  content: `
    <path d="..." />
    <circle cx="12" cy="12" r="3" />
  `
}
```

3. Run the generator: `node scripts/generate-ui-icons.js`

### Icon Categories
- `navigation`: Main navigation elements
- `actions`: User actions and interactions
- `status`: Status indicators and alerts
- `ui`: General UI elements
- `security`: Security and privacy related
- `data`: Data visualization and analytics

### Icon Types
- `stroke`: Outline icons (most common)
- `fill`: Filled/solid icons

## 🎨 Design System Integration

Icons are designed to match the fraud detection platform's design system:

- **Color Palette**: Uses CSS custom properties from the main theme
- **Sizing**: Consistent with Tailwind CSS spacing scale
- **Style**: Clean, minimal stroke-based design
- **Accessibility**: Proper ARIA labels and semantic markup

## 📁 File Structure

```
dashboard/
├── scripts/
│   ├── generate-ui-icons.js     # Main generator script
│   ├── generate-ui-icons.ts     # TypeScript version
│   └── README.md                # This file
├── src/
│   ├── components/
│   │   └── icons/               # Generated icon components
│   │       ├── DashboardIcon.tsx
│   │       ├── AlertTriangleIcon.tsx
│   │       ├── ...
│   │       └── index.ts         # Export file
│   └── types/
│       └── icons.ts             # TypeScript types
```

## 🔧 Technical Details

### No External Dependencies
- Uses only built-in Node.js and React capabilities
- No icon libraries (Feather, Heroicons, etc.) required
- Self-contained SVG generation

### TypeScript Support
- Full TypeScript definitions
- Proper prop typing
- IntelliSense support

### Performance
- Tree-shakeable imports
- Minimal bundle size impact
- SVG optimization

### Accessibility
- Semantic HTML structure
- ARIA labels
- Screen reader friendly
- Keyboard navigation support

## 🚀 Advanced Usage

### Custom Styling

```tsx
<DashboardIcon 
  size={28}
  color="#custom-color"
  strokeWidth={1.5}
  className="hover:text-blue-600 transition-colors"
  title="Dashboard Overview"
/>
```

### Event Handling

```tsx
<SearchIcon 
  size="lg"
  onClick={() => setSearchOpen(true)}
  className="cursor-pointer"
/>
```

### Conditional Rendering

```tsx
{isLoading ? (
  <RefreshIcon size="sm" className="animate-spin" />
) : (
  <CheckCircleIcon size="sm" color="success" />
)}
```

## 📝 License

This icon system is part of the fraud detection platform and follows the same licensing terms as the main project.
